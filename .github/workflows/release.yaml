name: releases

on:
  push:
    branches:
      - 'master'

jobs:
  bump-version:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2.3.4
        with:
          fetch-depth: 0
      - name: Bump version and push tag
        uses: anothrNick/github-tag-action@1.35.0
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          WITH_V: true
          DEFAULT_BUMP: patch
          RELEASE_BRANCHES: master
  goreleaser:
    runs-on: ubuntu-latest
    needs: bump-version
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
      - name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.16.3
      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v2
        with:
          version: latest
          args: release --rm-dist --skip-validate
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
