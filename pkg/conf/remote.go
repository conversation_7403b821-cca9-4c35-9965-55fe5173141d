package conf

import (
	"github.com/caarlos0/env/v6"
	"log"
)

// remote collections of environment and configuration variable
type remote struct {
	DBWriteHost         string   `env:"DATABASE_WRITE_HOST"`
	DBWritePort         string   `env:"DATABASE_WRITE_PORT"`
	DBWriteUser         string   `env:"DATABASE_WRITE_USER"`
	DBWritePass         string   `env:"DATABASE_WRITE_PASSWORD"`
	DBWriteName         string   `env:"DATABASE_WRITE_NAME"`
	DBReadHost          string   `env:"DATABASE_READ_HOST"`
	DBReadPort          string   `env:"DATABASE_READ_PORT"`
	DBReadUser          string   `env:"DATABASE_READ_USER"`
	DBReadPass          string   `env:"DATABASE_READ_PASSWORD"`
	DBReadName          string   `env:"DATABASE_READ_NAME"`
	DBMaxOpenConnection int      `env:"DATABASE_MAX_OPEN_CONNECTION"`
	DBMaxIdleConnection int      `env:"DATABASE_MAX_IDLE_CONNECTION"`
	DBTimeout           int      `env:"DATABASE_TIMEOUT"`
	UDLAddr             []string `env:"UDL_ADDRESS_REMOTE" envSeparator:","`
	Namespace           string   `env:"NAMESPACE"`
	Service             string   `env:"SERVICE"`
	Version             string   `env:"VERSION"`
	ServiceName         string   `env:"SERVICE_NAME"`
	ClusterID           string   `env:"CLUSTER_ID_REMOTE"`
	ClientID            string   `env:"CLIENT_ID"`
	PublishAsync        bool     `env:"PUBLISH_ASYNC_REMOTE" envDefault:"false"`
	UserCredentials     string   `env:"USER_CREDENTIALS_REMOTE"`                     // Certificate Sign Request from Istio Citadel Certificate Authority
	PersistenceName     string   `env:"PERSISTENCE_NAME_REMOTE"`                     // Persistence subscriber name
	PersistenceUnsub    bool     `env:"PERSISTENCE_UNSUB_REMOTE" envDefault:"false"` // Unsubscribe durable on exit
	Topic               string   `env:"TOPIC_REMOTE"`
	TopicError          string   `env:"TOPIC_ERROR"`
	TopicGroup          string   `env:"TOPIC_GROUP_REMOTE"`
	ShowData            bool     `env:"SHOW_DATA" envDefault:"false"`
}

var Remote = remote{}

func LoadEnvRemote() {
	// Load Engine
	if err := env.Parse(&Remote); err != nil {
		log.Panic(err.Error())
	}
}
