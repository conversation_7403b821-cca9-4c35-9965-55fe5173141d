package conf

import (
	"github.com/caarlos0/env/v6"
	"log"
)

// rest config
type rest struct {
	HTTPHost            string   `env:"HTTP_HOST" envDefault:"localhost"`
	HTTPPort            int      `env:"HTTP_PORT" envDefault:"3000"`
	DBHost              string   `env:"DATABASE_HOST" envDefault:"127.0.0.1"`
	DBPort              string   `env:"DATABASE_PORT" envDefault:"3306"`
	DBUser              string   `env:"DATABASE_USER" envDefault:"rskuserdb"`
	DBPass              string   `env:"DATABASE_PASSWORD" envDefault:"r5k453rd8!"`
	DBName              string   `env:"DATABASE_NAME" envDefault:"rskdb"`
	DBMaxOpenConnection int      `env:"DATABASE_MAX_OPEN_CONNECTION"`
	DBMaxIdleConnection int      `env:"DATABASE_MAX_IDLE_CONNECTION"`
	DBTimeout           int      `env:"DATABASE_TIMEOUT"`
	SSLMode             string   `env:"SSL_MODE" envDefault:"disable"`
	Debug               bool     `env:"DEBUG" envDefault:"false"`
	EnableJWT           bool     `env:"ENABLE_JWT" envDefault:"false"`
	EnableCache         bool     `env:"EnableCache" envDefault:"true"`
	HTTPSMode           bool     `env:"HTTPSMode" envDefault:"false"`
	SSLCert             string   `env:"SSL_CERT"`
	SSLKey              string   `env:"SSL_KEY"`
	SSLRootCert         string   `env:"SSL_ROOT_CERT"`
	JWTKey              string   `env:"JWT_KEY"`
	JWTAlgo             string   `env:"JWT_ALGO"`
	MigrationsPath      string   `env:"MIGRATION_PATH"`
	QueriesPath         string   `env:"QUERIES_PATH"`
	CORSAllowOrigin     []string `env:"CORS_ALLOW_ORIGIN" envSeparator:","`
	CORSAllowHeaders    []string `env:"CORS_ALLOW_HEADERS" envSeparator:","`
	HTTPSCert           string   `env:"HTTPS_CERT"`
	HTTPSKey            string   `env:"HTTPS_KEY"`
}

var Rest = rest{}

func LoadEnvRest() {
	// Load Engine
	if err := env.Parse(&Rest); err != nil {
		log.Panic(err.Error())
	}
}
