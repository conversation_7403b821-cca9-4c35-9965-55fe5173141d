package conf

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
	"unicode"
)

type parser struct {
	mapping  map[string]interface{}
	lx       *splitter
	ctx      interface{}
	ctxs     []interface{}
	keys     []string
	ikeys    []item
	fp       string
	pedantic bool
}

func Parse(data string) (map[string]interface{}, error) {
	p, err := parse(data, "", false)
	if err != nil {
		return nil, err
	}
	return p.mapping, nil
}

func ParseFile(fp string) (map[string]interface{}, error) {
	data, err := ioutil.ReadFile(fp)
	if err != nil {
		return nil, fmt.Errorf("error opening config file: %v", err)
	}
	p, err := parse(string(data), fp, false)
	if err != nil {
		return nil, err
	}
	return p.mapping, nil
}

func ParseFileChecks(fp string) (map[string]interface{}, error) {
	data, err := ioutil.ReadFile(fp)
	if err != nil {
		return nil, err
	}
	p, err := parse(string(data), fp, true)
	if err != nil {
		return nil, err
	}
	return p.mapping, nil
}

type token struct {
	item         item
	value        interface{}
	usedVariable bool
	sourceFile   string
}

func (t *token) Value() interface{} {
	return t.value
}

func (t *token) Line() int {
	return t.item.line
}

func (t *token) IsUsedVariable() bool {
	return t.usedVariable
}

func (t *token) SourceFile() string {
	return t.sourceFile
}

func (t *token) Position() int {
	return t.item.pos
}

func parse(data, fp string, pedantic bool) (p *parser, err error) {
	p = &parser{
		mapping:  make(map[string]interface{}),
		lx:       lex(data),
		ctxs:     make([]interface{}, 0, 4),
		keys:     make([]string, 0, 4),
		ikeys:    make([]item, 0, 4),
		fp:       filepath.Dir(fp),
		pedantic: pedantic,
	}
	p.pushContext(p.mapping)

	for {
		it := p.next()
		if it.typ == itemEOF {
			break
		}
		if err := p.processItem(it, fp); err != nil {
			return nil, err
		}
	}

	return p, nil
}

func (p *parser) next() item {
	return p.lx.nextItem()
}

func (p *parser) pushContext(ctx interface{}) {
	p.ctxs = append(p.ctxs, ctx)
	p.ctx = ctx
}

func (p *parser) popContext() interface{} {
	if len(p.ctxs) == 0 {
		panic("BUG in parser, context stack empty")
	}
	li := len(p.ctxs) - 1
	last := p.ctxs[li]
	p.ctxs = p.ctxs[0:li]
	p.ctx = p.ctxs[len(p.ctxs)-1]
	return last
}

func (p *parser) pushKey(key string) {
	p.keys = append(p.keys, key)
}

func (p *parser) popKey() string {
	if len(p.keys) == 0 {
		panic("BUG in parser, keys stack empty")
	}
	li := len(p.keys) - 1
	last := p.keys[li]
	p.keys = p.keys[0:li]
	return last
}

func (p *parser) pushItemKey(key item) {
	p.ikeys = append(p.ikeys, key)
}

func (p *parser) popItemKey() item {
	if len(p.ikeys) == 0 {
		panic("BUG in parser, item keys stack empty")
	}
	li := len(p.ikeys) - 1
	last := p.ikeys[li]
	p.ikeys = p.ikeys[0:li]
	return last
}

func (p *parser) processItem(it item, fp string) error {
	setValue := func(it item, v interface{}) {
		if p.pedantic {
			p.setValue(&token{it, v, false, fp})
		} else {
			p.setValue(v)
		}
	}

	switch it.typ {
	case itemError:
		return fmt.Errorf("Parse error on line %d: '%s'", it.line, it.val)
	case itemKey:
		p.pushKey(it.val)

		if p.pedantic {
			p.pushItemKey(it)
		}
	case itemMapStart:
		newCtx := make(map[string]interface{})
		p.pushContext(newCtx)
	case itemMapEnd:
		setValue(it, p.popContext())
	case itemString:
		setValue(it, it.val)
	case itemInteger:
		lastDigit := 0
		for _, r := range it.val {
			if !unicode.IsDigit(r) && r != '-' {
				break
			}
			lastDigit++
		}
		numStr := it.val[:lastDigit]
		num, err := strconv.ParseInt(numStr, 10, 64)
		if err != nil {
			if e, ok := err.(*strconv.NumError); ok &&
				e.Err == strconv.ErrRange {
				return fmt.Errorf("integer '%s' is out of the range", it.val)
			}
			return fmt.Errorf("expected integer, but got '%s'", it.val)
		}
		suffix := strings.ToLower(strings.TrimSpace(it.val[lastDigit:]))

		switch suffix {
		case "":
			setValue(it, num)
		case "k":
			setValue(it, num*1000)
		case "kb":
			setValue(it, num*1024)
		case "m":
			setValue(it, num*1000*1000)
		case "mb":
			setValue(it, num*1024*1024)
		case "g":
			setValue(it, num*1000*1000*1000)
		case "gb":
			setValue(it, num*1024*1024*1024)
		}
	case itemFloat:
		num, err := strconv.ParseFloat(it.val, 64)
		if err != nil {
			if e, ok := err.(*strconv.NumError); ok &&
				e.Err == strconv.ErrRange {
				return fmt.Errorf("float '%s' is out of the range", it.val)
			}
			return fmt.Errorf("expected float, but got '%s'", it.val)
		}
		setValue(it, num)
	case itemBool:
		switch strings.ToLower(it.val) {
		case "true", "yes", "on":
			setValue(it, true)
		case "false", "no", "off":
			setValue(it, false)
		default:
			return fmt.Errorf("expected boolean value, but got '%s'", it.val)
		}

	case itemDatetime:
		dt, err := time.Parse("2006-01-02T15:04:05Z", it.val)
		if err != nil {
			return fmt.Errorf(
				"expected Zulu formatted DateTime, but got '%s'", it.val)
		}
		setValue(it, dt)
	case itemArrayStart:
		var array = make([]interface{}, 0)
		p.pushContext(array)
	case itemArrayEnd:
		array := p.ctx
		p.popContext()
		setValue(it, array)
	case itemVariable:
		value, found, err := p.lookupVariable(it.val)
		if err != nil {
			return fmt.Errorf("variable reference for '%s' on line %d could not be parsed: %s",
				it.val, it.line, err)
		}
		if !found {
			return fmt.Errorf("variable reference for '%s' on line %d can not be found",
				it.val, it.line)
		}

		if p.pedantic {
			switch tk := value.(type) {
			case *token:
				tk.usedVariable = true
				p.setValue(&token{it, tk.Value(), false, fp})
			default:
				p.setValue(&token{it, value, false, fp})
			}
		} else {
			p.setValue(value)
		}
	case itemInclude:
		var (
			m   map[string]interface{}
			err error
		)
		if p.pedantic {
			m, err = ParseFileChecks(filepath.Join(p.fp, it.val))
		} else {
			m, err = ParseFile(filepath.Join(p.fp, it.val))
		}
		if err != nil {
			return fmt.Errorf("error parsing include file '%s', %v", it.val, err)
		}
		for k, v := range m {
			p.pushKey(k)

			if p.pedantic {
				switch tk := v.(type) {
				case *token:
					p.pushItemKey(tk.item)
				}
			}
			p.setValue(v)
		}
	}

	return nil
}

const pkey = "pk"
const bcryptPrefix = "2a$"

func (p *parser) lookupVariable(varReference string) (interface{}, bool, error) {
	if strings.HasPrefix(varReference, bcryptPrefix) {
		return "$" + varReference, true, nil
	}

	for i := len(p.ctxs) - 1; i >= 0; i-- {
		ctx := p.ctxs[i]
		if m, ok := ctx.(map[string]interface{}); ok {
			if v, ok := m[varReference]; ok {
				return v, ok, nil
			}
		}
	}

	if vStr, ok := os.LookupEnv(varReference); ok {
		if vmap, err := Parse(fmt.Sprintf("%s=%s", pkey, vStr)); err == nil {
			v, ok := vmap[pkey]
			return v, ok, nil
		} else {
			return nil, false, err
		}
	}
	return nil, false, nil
}

func (p *parser) setValue(val interface{}) {
	if ctx, ok := p.ctx.([]interface{}); ok {
		p.ctx = append(ctx, val)
		p.ctxs[len(p.ctxs)-1] = p.ctx
	}

	if ctx, ok := p.ctx.(map[string]interface{}); ok {
		key := p.popKey()
		if p.pedantic {
			switch v := val.(type) {
			case *token:
				it := p.popItemKey()
				v.item.pos = it.pos
				v.item.line = it.line
				ctx[key] = v
			}
		} else {
			ctx[key] = val
		}
	}
}
