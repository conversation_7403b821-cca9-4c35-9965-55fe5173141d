package conf

import (
	"github.com/caarlos0/env/v6"
	"log"
)

// shared collections of environment and configuration variable
type shared struct {
	Workspace     string `env:"WORKSPACE"`
	App           string `env:"APP"`
	Profiling     bool   `env:"PROFILING" envDefault:"false"`
	ShowPID       bool   `env:"SHOW_PID" envDefault:"true"`
	ShowTime      bool   `env:"SHOW_TIME" envDefault:"true"`
	StructuredLog bool   `env:"STRUCTURED_LOG" envDefault:"false"`
	Metrics       string `env:"METRICS"`
}

var Shared = shared{}

func LoadEnvShared() {
	// Load Engine
	if err := env.Parse(&Shared); err != nil {
		log.Panic(err.Error())
	}
}
