package conf

import (
	"github.com/caarlos0/env/v6"
	"log"
	"time"
)

type udl struct {
	Trace                 bool          `env:"TRACE" envDefault:"false"`
	Debug                 bool          `env:"DEBUG" envDefault:"false"`
	LogTime               bool          `env:"LOGTIME" envDefault:"false"`
	HTTP                  int           `env:"HTTP" envDefault:"8222"`
	ServerID              string        `env:"SERVER_ID" envDefault:"backend-omni"`
	ServerStore           string        `env:"SERVER_STORE" envDefault:"MEMORY"`
	ServerStoreDir        string        `env:"SERVER_STORE_DIR" envDefault:"/data/app/store"`
	FileOptionsBufferSize int           `env:"FILE_OPTIONS_BUFFER_SIZE" envDefault:"2"`
	ReadBufferSize        int           `env:"FILE_OPTIONS_READ_BUFFER_SIZE" envDefault:"2"`
	SyncOnFlush           bool          `env:"SYNC_ON_FLUSH" envDefault:"true"`
	SliceMaxBytes         int64         `env:"FILE_OPTIONS_SLICE_MAX_BYTES" envDefault:"64"`
	ParallelRecovery      int           `env:"FILE_OPTIONS_PARALLEL_RECOVERY" envDefault:"1"`
	MaxChannels           int           `env:"STORE_LIMIT_MAX_CHANNELS" envDefault:"100"`
	MaxMsgs               int           `env:"STORE_LIMIT_MAX_MASSAGES" envDefault:"1000000"`
	MaxBytes              int64         `env:"STORE_LIMIT_MAX_BYTES" envDefault:"1000000"`
	MaxAge                time.Duration `env:"STORE_LIMIT_MAX_AGE" envDefault:"0"`
	MaxSubscriptions      int           `env:"STORE_LIMIT_MAX_SUBSCRIPTION" envDefault:"1000"`
	MaxInactivity         time.Duration `env:"STORE_LIMIT_MAX_INACTIVITY" envDefault:"0"`
	MaxPayload            int32         `env:"MAX_PAYLOAD" envDefault:"0"`
}

var UDL = udl{}

func LoadEnvUDL() {
	if err := env.Parse(&UDL); err != nil {
		log.Panic(err.Error())
	}
}
