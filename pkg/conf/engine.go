package conf

import (
	"github.com/caarlos0/env/v6"
	"log"
)

// engine collections of environment and configuration variable
type engine struct {
	UDLAddr                       []string `env:"UDL_ADDRESS" envSeparator:","`
	ServiceName                   string   `env:"SERVICE_NAME"`
	Namespace                     string   `env:"NAMESPACE"`
	Service                       string   `env:"SERVICE"`
	Version                       string   `env:"VERSION"`
	ClusterID                     string   `env:"CLUSTER_ID"`
	ClientID                      string   `env:"CLIENT_ID"`
	PublishAsync                  bool     `env:"PUBLISH_ASYNC" envDefault:"false"`
	UserCredentials               []string `env:"USER_CREDENTIALS" envSeparator:","`    // Certificate Sign Request from Istio Citadel Certificate Authority
	PersistenceName               string   `env:"PERSISTENCE_NAME"`                     // Persistence subscriber name
	PersistenceUnsub              bool     `env:"PERSISTENCE_UNSUB" envDefault:"false"` // Unsubscribe durable on exit
	Topic                         string   `env:"TOPIC"`
	TopicGroup                    string   `env:"TOPIC_GROUP"`
	TopicAck                      string   `env:"TOPIC_ACK"`
	TopicGroupAck                 string   `env:"TOPIC_GROUP_ACK"`
	FilePrefix                    string   `env:"FILE_PREFIX"`
	WaitForReconnect              int      `env:"TOTAL_WAIT_FOR_RECONNECT_IN_HOUR"`
	ListeningDirectory            []string `env:"LISTENING_DIRECTORY" envSeparator:","`
	LineFile                      string   `env:"LINE_FILE"`
	SizeToSplit                   int64    `env:"SIZE_TO_SPLIT"`
	GCPProjectID                  []string `env:"GCP_PROJECT_ID" envSeparator:","`
	GCSBucketName                 []string `env:"GCS_BUCKET_NAME" envSeparator:","`
	GCSUploadDirectory            string   `env:"GCS_UPLOAD_DIRECTORY"`
	UploadDirectory               string   `env:"UPLOAD_DIRECTORY"`
	AcceptedFileExtensionToUpload string   `env:"ACCEPTED_FILE_EXTENSION_TO_UPLOAD"`
	GCSCredential                 []string `env:"GCS_CREDENTIAL" envSeparator:","`
	IsDebug                       bool     `env:"DEBUG"`
	Filter                        bool     `env:"FILTER" envDefault:"true"`
	TotalDirectory                int      `env:"TOTAL_DIRECTORY" envDefault:"10"`
	OmniDirPath                   string   `env:"OMNI_DIR_PATH"`
	FilestorePath                 string   `env:"FILESTORE_PATH"`
	MappingDir                    string   `env:"MAPPING_DIR"`
	SuccessDir                    string   `env:"SUCCESS_DIR"`
	FailDir                       string   `env:"FAIL_DIR"`
	EmptyDir                      string   `env:"EMPTY_DIR"`
	InvalidDir                    string   `env:"INVALID_DIR"`
	TopicPubSub                   string   `env:"TOPIC_PUBSUB"`
	SubscriberPubSub              string   `env:"SUBSCRIBER_PUBSUB"`
	MattermostChannelID           string   `env:"MATTERMOST_CHANNEL_ID"`
	BusinessUnitID                string   `env:"BUSINESS_UNIT_ID" envDefault:"TRTLMRT001"` // This used by PIM as CompanyCode
	EtcdEndpoints                 []string `env:"ETCD_ENDPOINTS" envSeparator:","`
	StorageConfig                 string   `env:"STORAGE_CONFIG"`
	LevelDBPath                   string   `env:"LEVELDB_PATH"`
	RecordData                    bool     `env:"RECORD_DATA" envDefault:"false"`
	BatchCount                    int32    `env:"BATCH_COUNT" envDefault:"10"`
	RestEndpoint                  string   `env:"REST_ENDPOINT"`
	UniformDistribution           bool     `env:"UNIFORM_DISTRIBUTION" envDefault:"true"`
	ExcludeFiles                  []string `env:"EXCLUDE_FILES" envSeparator:","`
	EnableInfluxDBExporter        bool     `env:"ENABLE_INFLUXDB_EXPORTER" envDefault:"false"`
	InfluxDBEndpoint              string   `env:"INFLUXDB_ENDPOINT"`
	InfluxDBDatabase              string   `env:"INFLUXDB_DATABASE"`
	InfluxDBUsername              string   `env:"INFLUXDB_USERNAME"`
	InfluxDBPassword              string   `env:"INFLUXDB_PASSWORD"`
	TraceFile                     string   `env:"TRACE_FILE"`
	CPUFile                       string   `env:"CPU_FILE"`
	BlockProfileFile              string   `env:"BLOCK_PROFILE_FILE"`
	MappingType                   int      `env:"MAPPING_TYPE" envDefault:"0"`
	MetricsNamespace              string   `env:"METRICS_NAMESPACE"`
}

var Engine = engine{}

func LoadEnvEngine() {
	// Load Engine
	if err := env.Parse(&Engine); err != nil {
		log.Panic(err.Error())
	}
}
