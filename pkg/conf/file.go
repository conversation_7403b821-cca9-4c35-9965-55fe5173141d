package conf

import (
	"encoding/hex"
	"fmt"
	"strings"
	"unicode"
	"unicode/utf8"
)

type itemType int

const (
	itemError itemType = iota
	itemNIL
	itemEOF
	itemKey
	itemText
	itemString
	itemBool
	itemInteger
	itemFloat
	itemDatetime
	itemArrayStart
	itemArrayEnd
	itemMapStart
	itemMapEnd
	itemCommentStart
	itemVariable
	itemInclude
)

const (
	eof               = 0
	mapStart          = '{'
	mapEnd            = '}'
	keySepEqual       = '='
	keySepColon       = ':'
	arrayStart        = '['
	arrayEnd          = ']'
	arrayValTerm      = ','
	mapValTerm        = ','
	commentHashStart  = '#'
	commentSlashStart = '/'
	dqStringStart     = '"'
	dqStringEnd       = '"'
	sqStringStart     = '\''
	sqStringEnd       = '\''
	optValTerm        = ';'
	topOptStart       = '{'
	topOptValTerm     = ','
	topOptTerm        = '}'
	blockStart        = '('
	blockEnd          = ')'
)

type stateFn func(sp *splitter) stateFn

type splitter struct {
	input         string
	start         int
	pos           int
	width         int
	line          int
	state         stateFn
	items         chan item
	stack         []stateFn
	stringParts   []string
	stringStateFn stateFn
	lstart        int
	ilstart       int
}

type item struct {
	typ  itemType
	val  string
	line int
	pos  int
}

func (sp *splitter) nextItem() item {
	for {
		select {
		case item := <-sp.items:
			return item
		default:
			sp.state = sp.state(sp)
		}
	}
}

func lex(input string) *splitter {
	lx := &splitter{
		input:       input,
		state:       spTop,
		line:        1,
		items:       make(chan item, 10),
		stack:       make([]stateFn, 0, 10),
		stringParts: []string{},
	}
	return lx
}

func (sp *splitter) push(state stateFn) {
	sp.stack = append(sp.stack, state)
}

func (sp *splitter) pop() stateFn {
	if len(sp.stack) == 0 {
		return sp.errorf("no states to pop.")
	}
	li := len(sp.stack) - 1
	last := sp.stack[li]
	sp.stack = sp.stack[0:li]
	return last
}

func (sp *splitter) emit(typ itemType) {
	val := strings.Join(sp.stringParts, "") + sp.input[sp.start:sp.pos]

	pos := sp.pos - sp.ilstart - len(val)
	sp.items <- item{typ, val, sp.line, pos}
	sp.start = sp.pos
	sp.ilstart = sp.lstart
}

func (sp *splitter) emitString() {
	var finalString string
	if len(sp.stringParts) > 0 {
		finalString = strings.Join(sp.stringParts, "") + sp.input[sp.start:sp.pos]
		sp.stringParts = []string{}
	} else {
		finalString = sp.input[sp.start:sp.pos]
	}
	pos := sp.pos - sp.ilstart - len(finalString)
	sp.items <- item{itemString, finalString, sp.line, pos}
	sp.start = sp.pos
	sp.ilstart = sp.lstart
}

func (sp *splitter) addCurrentStringPart(offset int) {
	sp.stringParts = append(sp.stringParts, sp.input[sp.start:sp.pos-offset])
	sp.start = sp.pos
}

func (sp *splitter) addStringPart(s string) stateFn {
	sp.stringParts = append(sp.stringParts, s)
	sp.start = sp.pos
	return sp.stringStateFn
}

func (sp *splitter) hasEscapedParts() bool {
	return len(sp.stringParts) > 0
}

func (sp *splitter) next() (r rune) {
	if sp.pos >= len(sp.input) {
		sp.width = 0
		return eof
	}

	if sp.input[sp.pos] == '\n' {
		sp.line++

		sp.lstart = sp.pos
	}
	r, sp.width = utf8.DecodeRuneInString(sp.input[sp.pos:])
	sp.pos += sp.width

	return r
}

func (sp *splitter) ignore() {
	sp.start = sp.pos
	sp.ilstart = sp.lstart
}

func (sp *splitter) backup() {
	sp.pos -= sp.width
	if sp.pos < len(sp.input) && sp.input[sp.pos] == '\n' {
		sp.line--
	}
}

func (sp *splitter) peek() rune {
	r := sp.next()
	sp.backup()
	return r
}

func (sp *splitter) errorf(format string, values ...interface{}) stateFn {
	for i, value := range values {
		if v, ok := value.(rune); ok {
			values[i] = escapeSpecial(v)
		}
	}

	pos := sp.pos - sp.lstart
	sp.items <- item{
		itemError,
		fmt.Sprintf(format, values...),
		sp.line,
		pos,
	}
	return nil
}

func spTop(sp *splitter) stateFn {
	r := sp.next()
	if unicode.IsSpace(r) {
		return lexSkip(sp, spTop)
	}

	switch r {
	case topOptStart:
		return lexSkip(sp, spTop)
	case commentHashStart:
		sp.push(spTop)
		return lexCommentStart
	case commentSlashStart:
		rn := sp.next()
		if rn == commentSlashStart {
			sp.push(spTop)
			return lexCommentStart
		}
		sp.backup()
		fallthrough
	case eof:
		if sp.pos > sp.start {
			return sp.errorf("Unexpected EOF.")
		}
		sp.emit(itemEOF)
		return nil
	}

	sp.backup()
	sp.push(lexTopValueEnd)
	return lexKeyStart
}

func lexTopValueEnd(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == commentHashStart:
		sp.push(spTop)
		return lexCommentStart
	case r == commentSlashStart:
		rn := sp.next()
		if rn == commentSlashStart {
			sp.push(spTop)
			return lexCommentStart
		}
		sp.backup()
		fallthrough
	case isWhitespace(r):
		return lexTopValueEnd
	case isNL(r) || r == eof || r == optValTerm || r == topOptValTerm || r == topOptTerm:
		sp.ignore()
		return spTop
	}
	return sp.errorf("Expected a top-level value to end with a new line, "+
		"comment or EOF, but got '%v' instead.", r)
}

func lexKeyStart(sp *splitter) stateFn {
	r := sp.peek()
	switch {
	case isKeySeparator(r):
		return sp.errorf("Unexpected key separator '%v'", r)
	case unicode.IsSpace(r):
		sp.next()
		return lexSkip(sp, lexKeyStart)
	case r == dqStringStart:
		sp.next()
		return lexSkip(sp, lexDubQuotedKey)
	case r == sqStringStart:
		sp.next()
		return lexSkip(sp, lexQuotedKey)
	}
	sp.ignore()
	sp.next()
	return lexKey
}

func lexDubQuotedKey(sp *splitter) stateFn {
	r := sp.peek()
	if r == dqStringEnd {
		sp.emit(itemKey)
		sp.next()
		return lexSkip(sp, lexKeyEnd)
	} else if r == eof {
		if sp.pos > sp.start {
			return sp.errorf("Unexpected EOF.")
		}
		sp.emit(itemEOF)
		return nil
	}
	sp.next()
	return lexDubQuotedKey
}

func lexQuotedKey(sp *splitter) stateFn {
	r := sp.peek()
	if r == sqStringEnd {
		sp.emit(itemKey)
		sp.next()
		return lexSkip(sp, lexKeyEnd)
	} else if r == eof {
		if sp.pos > sp.start {
			return sp.errorf("Unexpected EOF.")
		}
		sp.emit(itemEOF)
		return nil
	}
	sp.next()
	return lexQuotedKey
}

func (sp *splitter) keyCheckKeyword(fallThrough, push stateFn) stateFn {
	key := strings.ToLower(sp.input[sp.start:sp.pos])
	switch key {
	case "include":
		sp.ignore()
		if push != nil {
			sp.push(push)
		}
		return lexIncludeStart
	}
	sp.emit(itemKey)
	return fallThrough
}

func lexIncludeStart(sp *splitter) stateFn {
	r := sp.next()
	if isWhitespace(r) {
		return lexSkip(sp, lexIncludeStart)
	}
	sp.backup()
	return lexInclude
}

func lexIncludeQuotedString(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == sqStringEnd:
		sp.backup()
		sp.emit(itemInclude)
		sp.next()
		sp.ignore()
		return sp.pop()
	}
	return lexIncludeQuotedString
}

func lexIncludeDubQuotedString(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == dqStringEnd:
		sp.backup()
		sp.emit(itemInclude)
		sp.next()
		sp.ignore()
		return sp.pop()
	}
	return lexIncludeDubQuotedString
}

func lexIncludeString(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case isNL(r) || r == eof || r == optValTerm || r == mapEnd || isWhitespace(r):
		sp.backup()
		sp.emit(itemInclude)
		return sp.pop()
	case r == sqStringEnd:
		sp.backup()
		sp.emit(itemInclude)
		sp.next()
		sp.ignore()
		return sp.pop()
	}
	return lexIncludeString
}

func lexInclude(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == sqStringStart:
		sp.ignore()
		return lexIncludeQuotedString
	case r == dqStringStart:
		sp.ignore()
		return lexIncludeDubQuotedString
	case r == arrayStart:
		return sp.errorf("Expected include value but found start of an array")
	case r == mapStart:
		return sp.errorf("Expected include value but found start of a map")
	case r == blockStart:
		return sp.errorf("Expected include value but found start of a block")
	case unicode.IsDigit(r), r == '-':
		return sp.errorf("Expected include value but found start of a number")
	case r == '\\':
		return sp.errorf("Expected include value but found escape sequence")
	case isNL(r):
		return sp.errorf("Expected include value but found new line")
	}
	sp.backup()
	return lexIncludeString
}

func lexKey(sp *splitter) stateFn {
	r := sp.peek()
	if unicode.IsSpace(r) {
		return sp.keyCheckKeyword(lexKeyEnd, nil)
	} else if isKeySeparator(r) || r == eof {
		sp.emit(itemKey)
		return lexKeyEnd
	}
	sp.next()
	return lexKey
}

func lexKeyEnd(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case unicode.IsSpace(r):
		return lexSkip(sp, lexKeyEnd)
	case isKeySeparator(r):
		return lexSkip(sp, lexValue)
	case r == eof:
		sp.emit(itemEOF)
		return nil
	}
	sp.backup()
	return lexValue
}

func lexValue(sp *splitter) stateFn {
	r := sp.next()
	if isWhitespace(r) {
		return lexSkip(sp, lexValue)
	}

	switch {
	case r == arrayStart:
		sp.ignore()
		sp.emit(itemArrayStart)
		return lexArrayValue
	case r == mapStart:
		sp.ignore()
		sp.emit(itemMapStart)
		return lexMapKeyStart
	case r == sqStringStart:
		sp.ignore()
		return lexQuotedString
	case r == dqStringStart:
		sp.ignore()
		sp.stringStateFn = lexDubQuotedString
		return lexDubQuotedString
	case r == '-':
		return lexNegNumberStart
	case r == blockStart:
		sp.ignore()
		return lexBlock
	case unicode.IsDigit(r):
		sp.backup()
		return lexNumberOrDateOrStringOrIPStart
	case r == '.':
		return sp.errorf("Floats must start with a digit")
	case isNL(r):
		return sp.errorf("Expected value but found new line")
	}
	sp.backup()
	sp.stringStateFn = lexString
	return lexString
}

func lexArrayValue(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case unicode.IsSpace(r):
		return lexSkip(sp, lexArrayValue)
	case r == commentHashStart:
		sp.push(lexArrayValue)
		return lexCommentStart
	case r == commentSlashStart:
		rn := sp.next()
		if rn == commentSlashStart {
			sp.push(lexArrayValue)
			return lexCommentStart
		}
		sp.backup()
		fallthrough
	case r == arrayValTerm:
		return sp.errorf("Unexpected array value terminator '%v'.", arrayValTerm)
	case r == arrayEnd:
		return lexArrayEnd
	}

	sp.backup()
	sp.push(splitArrayValueEnd)
	return lexValue
}

func splitArrayValueEnd(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case isWhitespace(r):
		return lexSkip(sp, splitArrayValueEnd)
	case r == commentHashStart:
		sp.push(splitArrayValueEnd)
		return lexCommentStart
	case r == commentSlashStart:
		rn := sp.next()
		if rn == commentSlashStart {
			sp.push(splitArrayValueEnd)
			return lexCommentStart
		}
		sp.backup()
		fallthrough
	case r == arrayValTerm || isNL(r):
		return lexSkip(sp, lexArrayValue)
	case r == arrayEnd:
		return lexArrayEnd
	}
	return sp.errorf("Expected an array value terminator %q or an array "+
		"terminator %q, but got '%v' instead.", arrayValTerm, arrayEnd, r)
}

func lexArrayEnd(sp *splitter) stateFn {
	sp.ignore()
	sp.emit(itemArrayEnd)
	return sp.pop()
}

func lexMapKeyStart(sp *splitter) stateFn {
	r := sp.peek()
	switch {
	case isKeySeparator(r):
		return sp.errorf("Unexpected key separator '%v'.", r)
	case r == arrayEnd:
		return sp.errorf("Unexpected array end '%v' processing map.", r)
	case unicode.IsSpace(r):
		sp.next()
		return lexSkip(sp, lexMapKeyStart)
	case r == mapEnd:
		sp.next()
		return lexSkip(sp, lexMapEnd)
	case r == commentHashStart:
		sp.next()
		sp.push(lexMapKeyStart)
		return lexCommentStart
	case r == commentSlashStart:
		sp.next()
		rn := sp.next()
		if rn == commentSlashStart {
			sp.push(lexMapKeyStart)
			return lexCommentStart
		}
		sp.backup()
	case r == sqStringStart:
		sp.next()
		return lexSkip(sp, lexMapQuotedKey)
	case r == dqStringStart:
		sp.next()
		return lexSkip(sp, lexMapDubQuotedKey)
	case r == eof:
		return sp.errorf("Unexpected EOF processing map.")
	}
	sp.ignore()
	sp.next()
	return lexMapKey
}

func lexMapQuotedKey(sp *splitter) stateFn {
	r := sp.peek()
	if r == sqStringEnd {
		sp.emit(itemKey)
		sp.next()
		return lexSkip(sp, lexMapKeyEnd)
	}
	sp.next()
	return lexMapQuotedKey
}

func lexMapDubQuotedKey(sp *splitter) stateFn {
	r := sp.peek()
	if r == dqStringEnd {
		sp.emit(itemKey)
		sp.next()
		return lexSkip(sp, lexMapKeyEnd)
	}
	sp.next()
	return lexMapDubQuotedKey
}

func lexMapKey(sp *splitter) stateFn {
	r := sp.peek()
	if unicode.IsSpace(r) {
		return sp.keyCheckKeyword(lexMapKeyEnd, lexMapValueEnd)
	} else if isKeySeparator(r) {
		sp.emit(itemKey)
		return lexMapKeyEnd
	}
	sp.next()
	return lexMapKey
}

func lexMapKeyEnd(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case unicode.IsSpace(r):
		return lexSkip(sp, lexMapKeyEnd)
	case isKeySeparator(r):
		return lexSkip(sp, lexMapValue)
	}
	sp.backup()
	return lexMapValue
}

func lexMapValue(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case unicode.IsSpace(r):
		return lexSkip(sp, lexMapValue)
	case r == mapValTerm:
		return sp.errorf("Unexpected map value terminator %q.", mapValTerm)
	case r == mapEnd:
		return lexSkip(sp, lexMapEnd)
	}
	sp.backup()
	sp.push(lexMapValueEnd)
	return lexValue
}

func lexMapValueEnd(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case isWhitespace(r):
		return lexSkip(sp, lexMapValueEnd)
	case r == commentHashStart:
		sp.push(lexMapValueEnd)
		return lexCommentStart
	case r == commentSlashStart:
		rn := sp.next()
		if rn == commentSlashStart {
			sp.push(lexMapValueEnd)
			return lexCommentStart
		}
		sp.backup()
		fallthrough
	case r == optValTerm || r == mapValTerm || isNL(r):
		return lexSkip(sp, lexMapKeyStart)
	case r == mapEnd:
		return lexSkip(sp, lexMapEnd)
	}
	return sp.errorf("Expected a map value terminator %q or a map "+
		"terminator %q, but got '%v' instead.", mapValTerm, mapEnd, r)
}

func lexMapEnd(sp *splitter) stateFn {
	sp.ignore()
	sp.emit(itemMapEnd)
	return sp.pop()
}

func (sp *splitter) isBool() bool {
	str := strings.ToLower(sp.input[sp.start:sp.pos])
	return str == "true" || str == "false" ||
		str == "on" || str == "off" ||
		str == "yes" || str == "no"
}

func (sp *splitter) isVariable() bool {
	if sp.input[sp.start] == '$' {
		sp.start += 1
		return true
	}
	return false
}

func lexQuotedString(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == sqStringEnd:
		sp.backup()
		sp.emit(itemString)
		sp.next()
		sp.ignore()
		return sp.pop()
	case r == eof:
		if sp.pos > sp.start {
			return sp.errorf("Unexpected EOF.")
		}
		sp.emit(itemEOF)
		return nil
	}
	return lexQuotedString
}

func lexDubQuotedString(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == '\\':
		sp.addCurrentStringPart(1)
		return lexStringEscape
	case r == dqStringEnd:
		sp.backup()
		sp.emitString()
		sp.next()
		sp.ignore()
		return sp.pop()
	case r == eof:
		if sp.pos > sp.start {
			return sp.errorf("Unexpected EOF.")
		}
		sp.emit(itemEOF)
		return nil
	}
	return lexDubQuotedString
}

func lexString(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == '\\':
		sp.addCurrentStringPart(1)
		return lexStringEscape
	case isNL(r) || r == eof || r == optValTerm ||
		r == arrayValTerm || r == arrayEnd || r == mapEnd ||
		isWhitespace(r):

		sp.backup()
		if sp.hasEscapedParts() {
			sp.emitString()
		} else if sp.isBool() {
			sp.emit(itemBool)
		} else if sp.isVariable() {
			sp.emit(itemVariable)
		} else {
			sp.emitString()
		}
		return sp.pop()
	case r == sqStringEnd:
		sp.backup()
		sp.emitString()
		sp.next()
		sp.ignore()
		return sp.pop()
	}
	return lexString
}

func lexBlock(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == blockEnd:
		sp.backup()
		sp.backup()

		if sp.next() != '\n' {
			sp.next()
			break
		}
		sp.next()

		switch sp.next() {
		case '\n', eof:
			sp.backup()
			sp.backup()
			sp.emit(itemString)
			sp.next()
			sp.ignore()
			return sp.pop()
		}
		sp.backup()
	case r == eof:
		return sp.errorf("Unexpected EOF processing block.")
	}
	return lexBlock
}

func lexStringEscape(sp *splitter) stateFn {
	r := sp.next()
	switch r {
	case 'x':
		return lexStringBinary
	case 't':
		return sp.addStringPart("\t")
	case 'n':
		return sp.addStringPart("\n")
	case 'r':
		return sp.addStringPart("\r")
	case '"':
		return sp.addStringPart("\"")
	case '\\':
		return sp.addStringPart("\\")
	}
	return sp.errorf("Invalid escape character '%v'. Only the following "+
		"escape characters are allowed: \\xXX, \\t, \\n, \\r, \\\", \\\\.", r)
}

func lexStringBinary(sp *splitter) stateFn {
	r := sp.next()
	if isNL(r) {
		return sp.errorf("Expected two hexadecimal digits after '\\x', but hit end of line")
	}
	r = sp.next()
	if isNL(r) {
		return sp.errorf("Expected two hexadecimal digits after '\\x', but hit end of line")
	}
	offset := sp.pos - 2
	byteString, err := hex.DecodeString(sp.input[offset:sp.pos])
	if err != nil {
		return sp.errorf("Expected two hexadecimal digits after '\\x', but got '%s'", sp.input[offset:sp.pos])
	}
	sp.addStringPart(string(byteString))
	return sp.stringStateFn
}

func lexNumberOrDateOrStringOrIPStart(sp *splitter) stateFn {
	r := sp.next()
	if !unicode.IsDigit(r) {
		if r == '.' {
			return sp.errorf("Floats must start with a digit, not '.'.")
		}
		return sp.errorf("Expected a digit but got '%v'.", r)
	}
	return lexNumberOrDateOrStringOrIP
}

func lexNumberOrDateOrStringOrIP(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == '-':
		if sp.pos-sp.start != 5 {
			return sp.errorf("All ISO8601 dates must be in full Zulu form.")
		}
		return lexDateAfterYear
	case unicode.IsDigit(r):
		return lexNumberOrDateOrStringOrIP
	case r == '.':
		return lexFloatStart
	case isNumberSuffix(r):
		return lexConvenientNumber
	case !(isNL(r) || r == eof || r == mapEnd || r == optValTerm || r == mapValTerm || isWhitespace(r) || unicode.IsDigit(r)):
		return lexString
	}
	sp.backup()
	sp.emit(itemInteger)
	return sp.pop()
}

func lexConvenientNumber(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case r == 'b' || r == 'B':
		return lexConvenientNumber
	}
	sp.backup()
	sp.emit(itemInteger)
	return sp.pop()
}

func lexDateAfterYear(sp *splitter) stateFn {
	formats := []rune{
		'0', '0', '-', '0', '0',
		'T',
		'0', '0', ':', '0', '0', ':', '0', '0',
		'Z',
	}
	for _, f := range formats {
		r := sp.next()
		if f == '0' {
			if !unicode.IsDigit(r) {
				return sp.errorf("Expected digit in ISO8601 datetime, "+
					"but found '%v' instead.", r)
			}
		} else if f != r {
			return sp.errorf("Expected '%v' in ISO8601 datetime, "+
				"but found '%v' instead.", f, r)
		}
	}
	sp.emit(itemDatetime)
	return sp.pop()
}

func lexNegNumberStart(sp *splitter) stateFn {
	r := sp.next()
	if !unicode.IsDigit(r) {
		if r == '.' {
			return sp.errorf("Floats must start with a digit, not '.'.")
		}
		return sp.errorf("Expected a digit but got '%v'.", r)
	}
	return lexNegNumber
}

func lexNegNumber(sp *splitter) stateFn {
	r := sp.next()
	switch {
	case unicode.IsDigit(r):
		return lexNegNumber
	case r == '.':
		return lexFloatStart
	case isNumberSuffix(r):
		return lexConvenientNumber
	}
	sp.backup()
	sp.emit(itemInteger)
	return sp.pop()
}

func lexFloatStart(sp *splitter) stateFn {
	r := sp.next()
	if !unicode.IsDigit(r) {
		return sp.errorf("Floats must have a digit after the '.', but got "+
			"'%v' instead.", r)
	}
	return lexFloat
}

func lexFloat(sp *splitter) stateFn {
	r := sp.next()
	if unicode.IsDigit(r) {
		return lexFloat
	}

	if r == '.' {
		return lexIPAddr
	}

	sp.backup()
	sp.emit(itemFloat)
	return sp.pop()
}

func lexIPAddr(sp *splitter) stateFn {
	r := sp.next()
	if unicode.IsDigit(r) || r == '.' || r == ':' || r == '-' {
		return lexIPAddr
	}
	sp.backup()
	sp.emit(itemString)
	return sp.pop()
}

func lexCommentStart(sp *splitter) stateFn {
	sp.ignore()
	sp.emit(itemCommentStart)
	return lexComment
}

func lexComment(sp *splitter) stateFn {
	r := sp.peek()
	if isNL(r) || r == eof {
		sp.emit(itemText)
		return sp.pop()
	}
	sp.next()
	return lexComment
}

func lexSkip(sp *splitter, nextState stateFn) stateFn {
	return func(sp *splitter) stateFn {
		sp.ignore()
		return nextState
	}
}

func isNumberSuffix(r rune) bool {
	return r == 'k' || r == 'K' || r == 'm' || r == 'M' || r == 'g' || r == 'G'
}

func isKeySeparator(r rune) bool {
	return r == keySepEqual || r == keySepColon
}

func isWhitespace(r rune) bool {
	return r == '\t' || r == ' '
}

func isNL(r rune) bool {
	return r == '\n' || r == '\r'
}

func (itype itemType) String() string {
	switch itype {
	case itemError:
		return "Error"
	case itemNIL:
		return "NIL"
	case itemEOF:
		return "EOF"
	case itemText:
		return "Text"
	case itemString:
		return "String"
	case itemBool:
		return "Bool"
	case itemInteger:
		return "Integer"
	case itemFloat:
		return "Float"
	case itemDatetime:
		return "DateTime"
	case itemKey:
		return "Key"
	case itemArrayStart:
		return "ArrayStart"
	case itemArrayEnd:
		return "ArrayEnd"
	case itemMapStart:
		return "MapStart"
	case itemMapEnd:
		return "MapEnd"
	case itemCommentStart:
		return "CommentStart"
	case itemVariable:
		return "Variable"
	case itemInclude:
		return "Include"
	}
	panic(fmt.Sprintf("BUG: Unknown type '%s'.", itype.String()))
}

func (item item) String() string {
	return fmt.Sprintf("(%s, '%s', %d, %d)", item.typ.String(), item.val, item.line, item.pos)
}

func escapeSpecial(c rune) string {
	switch c {
	case '\n':
		return "\\n"
	}
	return string(c)
}
