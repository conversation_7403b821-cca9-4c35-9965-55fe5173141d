package trade

import (
	"context"
	"flag"
	"fmt"
	"github.com/adshao/go-binance/v2"
	"github.com/dariubs/percent"
	"log"
	"math/big"
	"strconv"
	"strings"
)

type Order struct {
	Symbol    string
	Coin      string
	Market    string
	Qty       string
	Multiply  float64
	BuyPrice  float64
	SellPrice float64
	OrderID   int64
}

type Trade struct {
	Order
	*BinanceClient
	Cancel       string
	SellToCancel string
	Asset        float64
	Print        bool
}

func NewTrade() *Trade {
	return &Trade{
		BinanceClient: NewBinanceClient(),
	}
}

func NewTradeFromCLI() *Trade {
	m := NewTrade()
	m.Print = true
	flag.StringVar(&m.Qty, "quantity", "", "quantity")
	flag.StringVar(&m.Market, "qa", "BTC", "Quote Asset")
	flag.StringVar(&m.Coin, "ba", "", "Base Asset")
	flag.Float64Var(&m.Multiply, "multiply", 1.1, "multiply")
	flag.Float64Var(&m.BuyPrice, "buy", 0, "buy price")
	flag.Float64Var(&m.SellPrice, "sell", 0, "sell price")
	flag.Float64Var(&m.Asset, "asset", 0, "asset value")
	flag.Float64Var(&m.SellPrice, "sp", 0, "sell price")
	cancel := flag.String("c", "", "Cancel order without _, ex: ATMBTC. Currently only support symbol with 1 open order")
	flag.StringVar(&m.SellToCancel, "sc", "", "Cancel order without _, ex: ATMBTC. Currently only support symbol with 1 open order")
	flag.Parse()
	m.Symbol = fmt.Sprintf("%s%s", m.Coin, m.Market)
	if *cancel != "" {
		m.Cancel = fmt.Sprintf("%s%s", *cancel, m.Market)
	}
	m.SetAsset()

	return m
}

func (t *Trade) SetAsset() {
	if t.Asset > 0 {
		return
	}
	switch t.Market {
	case "BTC":
		t.Asset = 0.005
	case "BNB":
		t.Asset = 1
	case "ETH":
		t.Asset = 0.1
	}
}

func (t *Trade) SetMultiply() {
	if t.Multiply > 0 {
		return
	}
	switch t.Market {
	case "BTC":
		t.Multiply = 1.1
	case "BNB":
		t.Multiply = 1.1
	case "ETH":
		t.Multiply = 1.1
	}
}

func (t *Trade) CancelOrder() {
	openOrders, err := t.Spot.NewListOpenOrdersService().Do(context.Background())
	if err != nil {
		log.Fatal(err)
	}
	for _, order := range openOrders {
		if order.Symbol == t.Cancel {
			fmt.Println(order.Price, t.SellToCancel)
			if t.SellToCancel != "" {
				if strings.Contains(order.Price, t.SellToCancel) {
					t.CancellingOrder(order)
					break
				}
			} else {
				t.CancellingOrder(order)
				break
			}
		}
	}
}

func (t *Trade) CancellingOrder(order *binance.Order) {
	a, err := t.Spot.NewCancelOrderService().Symbol(t.Cancel).
		OrderID(order.OrderID).Do(t.Ctx)
	if err != nil {
		log.Fatalln(err)
	}
	avg, asset, err := t.SellMarket(order.OrigQuantity, order.Symbol)
	if err != nil {
		log.Fatalf("err when SellMarket with err %v\f", err)
	}
	t.Asset = asset
	sellOrderPrice, _ := strconv.ParseFloat(a.Price, 64)
	t.Multiply = (percent.ChangeFloat(avg, sellOrderPrice) / 100) + 1 + 0.02
	if t.Print {
		fmt.Printf("cutloss price %v, sell order price %v, change %v\n", avg, sellOrderPrice, t.Multiply)
	}
	t.Trading()
}

func (t *Trade) Buy() (a float64, b float64, err error) {
	for i := 3; i >= 0; i-- {
		qtyFl, _ := strconv.ParseFloat(t.Qty, 64)
		t.Qty = big.NewFloat(qtyFl).SetMode(big.AwayFromZero).Text('f', i)
		or, err := t.Spot.NewCreateOrderService().Symbol(t.Symbol).
			Side(binance.SideTypeBuy).Type(binance.OrderTypeMarket).
			Quantity(t.Qty).
			Do(t.Ctx)
		if err != nil {
			if strings.Contains(err.Error(), "Filter failure: LOT_SIZE") || strings.Contains(err.Error(), "Invalid quantity") {
				continue
			}
			return 0, 0, err
		}
		return averagePrice(or.Fills)
	}
	return 0, 0, err
}

func averagePrice(fills []*binance.Fill) (float64, float64, error) {
	var sv, sq float64
	for _, fill := range fills {
		p, _ := strconv.ParseFloat(fill.Price, 64)
		q, _ := strconv.ParseFloat(fill.Quantity, 64)
		sv += p * q
		sq += q
	}
	return sv / sq, sv, nil
}

func (t *Trade) SellLimit() (err error) {
	// if sell price is not provided then multiply buy price with given multiply
	if t.SellPrice == 0 {
		t.SellPrice = t.BuyPrice * t.Multiply
		if t.Print {
			fmt.Printf("%v = %v * %v \n", t.SellPrice, t.BuyPrice, t.Multiply)
		}
	}
	for i := 8; i >= 0; i-- {
		sp := big.NewFloat(t.SellPrice).SetMode(big.AwayFromZero).Text('f', i)
		_, err = t.Spot.NewCreateOrderService().Symbol(t.Symbol).
			Side(binance.SideTypeSell).Type(binance.OrderTypeLimit).
			TimeInForce(binance.TimeInForceTypeGTC).Quantity(t.Qty).
			Price(sp).Do(t.Ctx)
		if err != nil {
			if strings.Contains(err.Error(), "Filter failure: PRICE_FILTER") || strings.Contains(err.Error(), "Invalid price") {
				continue
			}
			return err
		}
		return err
	}
	return err
}

func (t *BinanceClient) SellMarket(qty, symbol string) (f, f2 float64, err error) {
	for i := 3; i >= 0; i-- {
		qtyFl, _ := strconv.ParseFloat(qty, 64)
		qty = big.NewFloat(qtyFl).SetMode(big.AwayFromZero).Text('f', i)
		or, err := t.Spot.NewCreateOrderService().Symbol(symbol).
			Side(binance.SideTypeSell).Type(binance.OrderTypeMarket).
			Quantity(qty).
			Do(t.Ctx)
		if err != nil {
			if strings.Contains(err.Error(), "Filter failure: LOT_SIZE") {
				continue
			}
			break
		}
		return averagePrice(or.Fills)
	}
	return 0, 0, err
}

func (t *Trade) Trading() Row {
	t.SetQtyByGivenAsset()
	if t.BuyPrice == 0 {
		if price, _, err := t.Buy(); err != nil {
			log.Fatalf("error when buy %v", err)
		} else {
			t.BuyPrice = price
			if t.Print {
				fmt.Printf("buy price %v\n", price)
			}
			if err = t.SellLimit(); err != nil {
				log.Fatalf("err when sell limit %v\n", err)
			}
		}
		return Row{
			Info: Info{
				Buy:  t.BuyPrice,
				Sell: t.SellPrice,
			},
			Market: t.Market,
			Coin:   t.Coin,
			Symbol: t.Symbol,
		}
	}
	return Row{}
}

func (t *Trade) SetQtyByGivenAsset() {
	if t.Qty == "" && t.Asset > 0 {
		prices, err := t.Spot.NewListPricesService().Do(t.Ctx)
		if err != nil {
			log.Fatalln(err)
		}
		for _, p := range prices {
			if p.Symbol == t.Symbol {
				priceFloat, err := strconv.ParseFloat(p.Price, 64)
				if err != nil {
					log.Fatalln(err)
				}
				t.Qty = fmt.Sprintf("%.2f", t.Asset/priceFloat)
				break
			}
		}
	}
}
