package trade

import (
	"context"
	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/delivery"
	"github.com/adshao/go-binance/v2/futures"
	"github.com/yudaprama/binance/pkg/conf"
)

type BinanceClient struct {
	Spot     *binance.Client
	Ctx      context.Context
	Futures  *futures.Client
	Delivery *delivery.Client
}

func NewBinanceClient() *BinanceClient {
	return &BinanceClient{
		Spot:     binance.NewClient(conf.Binance<PERSON><PERSON><PERSON>ey, conf.BinanceSecretKey),
		Futures:  futures.NewClient(conf.BinanceApi<PERSON>ey, conf.BinanceSecretKey),
		Delivery: delivery.NewClient(conf.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, conf.BinanceSecretKey),
		Ctx:      context.Background(),
	}
}
