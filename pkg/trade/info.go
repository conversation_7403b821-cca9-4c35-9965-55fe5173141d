package trade

import (
	"bytes"
	"encoding/gob"
	"log"
)

type Info struct {
	Buy  float64
	Sell float64
}

func (o Info) EncodeToBytes() []byte {
	buf := bytes.Buffer{}
	enc := gob.NewEncoder(&buf)
	err := enc.Encode(o)
	if err != nil {
		log.Fatalf("err encode trade info with err %v\n", err)
	}
	return buf.Bytes()
}

func DecodeToTradeInfo(s []byte) Info {
	p := Info{}
	dec := gob.NewDecoder(bytes.NewReader(s))
	err := dec.Decode(&p)
	if err != nil {
		log.Fatalf("err when decode trade info with err %v\n", err)
	}
	return p
}

type Language string

const (
	EnUS Language = "en_US"
)

type MatchingUnitType string

const (
	Standard MatchingUnitType = "STANDARD"
)

type MsgAuth string

const (
	Normal MsgAuth = "NORMAL"
)

type OrderType string

const (
	Limit  OrderType = "Limit"
	Market OrderType = "Market"
)

type Status string

const (
	Canceled Status = "Canceled"
	Filled   Status = "Filled"
)

type QuoteAsset string

const (
	Bnb QuoteAsset = "BNB"
	Btc QuoteAsset = "BTC"
	Eth QuoteAsset = "ETH"
)

type Side string

const (
	Buy  Side = "BUY"
	Sell Side = "SELL"
)

type Type string

const (
	TypeLIMIT  Type = "LIMIT"
	TypeMARKET Type = "MARKET"
)
