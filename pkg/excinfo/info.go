package excinfo

import (
	"encoding/json"
	"io"
	"net/http"
)

const (
	futureEndpoint = "https://www.binance.com/fapi/v1/exchangeInfo"
	spotEndpoint   = "https://www.binance.com/api/v1/exchangeInfo"
)

func GetFuturesExcInfo() []Symbol {
	return GetExcInfo(futureEndpoint)
}

func GetSpotExcInfo() []Symbol {
	return GetExcInfo(spotEndpoint)
}

func GetExcInfo(url string) []Symbol {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		panic(err)
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	r := UnmarshalWelcome(resp.Body)
	return r.Symbols
}

func UnmarshalWelcome(data io.Reader) ExchangeInfo {
	var r ExchangeInfo
	if err := json.NewDecoder(data).Decode(&r); err != nil {
		panic(err)
	}
	return r
}

func (r *ExchangeInfo) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

type ExchangeInfo struct {
	Timezone        string        `json:"timezone"`
	ServerTime      int64         `json:"serverTime"`
	FuturesType     string        `json:"futuresType"`
	RateLimits      []RateLimit   `json:"rateLimits"`
	ExchangeFilters []interface{} `json:"exchangeFilters"`
	Symbols         []Symbol      `json:"symbols"`
}

type RateLimit struct {
	RateLimitType string `json:"rateLimitType"`
	Interval      string `json:"interval"`
	IntervalNum   int64  `json:"intervalNum"`
	Limit         int64  `json:"limit"`
}

type Symbol struct {
	Symbol                string              `json:"symbol"`
	Pair                  string              `json:"pair"`
	ContractType          ContractType        `json:"contractType"`
	DeliveryDate          int64               `json:"deliveryDate"`
	OnboardDate           int64               `json:"onboardDate"`
	Status                Status              `json:"status"`
	MaintMarginPercent    string              `json:"maintMarginPercent"`
	RequiredMarginPercent string              `json:"requiredMarginPercent"`
	BaseAsset             string              `json:"baseAsset"`
	QuoteAsset            Asset               `json:"quoteAsset"`
	MarginAsset           Asset               `json:"marginAsset"`
	PricePrecision        int64               `json:"pricePrecision"`
	QuantityPrecision     int64               `json:"quantityPrecision"`
	BaseAssetPrecision    int64               `json:"baseAssetPrecision"`
	QuotePrecision        int64               `json:"quotePrecision"`
	UnderlyingType        UnderlyingType      `json:"underlyingType"`
	UnderlyingSubType     []UnderlyingSubType `json:"underlyingSubType"`
	SettlePlan            int64               `json:"settlePlan"`
	TriggerProtect        string              `json:"triggerProtect"`
	Filters               []Filter            `json:"filters"`
	OrderTypes            []OrderType         `json:"orderTypes"`
	TimeInForce           []TimeInForce       `json:"timeInForce"`
}

type Filter struct {
	MinPrice          *string    `json:"minPrice,omitempty"`
	MaxPrice          *string    `json:"maxPrice,omitempty"`
	FilterType        FilterType `json:"filterType"`
	TickSize          *string    `json:"tickSize,omitempty"`
	StepSize          *string    `json:"stepSize,omitempty"`
	MaxQty            *string    `json:"maxQty,omitempty"`
	MinQty            *string    `json:"minQty,omitempty"`
	Limit             *int64     `json:"limit,omitempty"`
	Notional          *string    `json:"notional,omitempty"`
	MultiplierDown    *string    `json:"multiplierDown,omitempty"`
	MultiplierUp      *string    `json:"multiplierUp,omitempty"`
	MultiplierDecimal *string    `json:"multiplierDecimal,omitempty"`
}

type ContractType string

const (
	CurrentQuarter ContractType = "CURRENT_QUARTER"
	Perpetual      ContractType = "PERPETUAL"
)

type FilterType string

const (
	LotSize          FilterType = "LOT_SIZE"
	MarketLotSize    FilterType = "MARKET_LOT_SIZE"
	MaxNumAlgoOrders FilterType = "MAX_NUM_ALGO_ORDERS"
	MaxNumOrders     FilterType = "MAX_NUM_ORDERS"
	MinNotional      FilterType = "MIN_NOTIONAL"
	PercentPrice     FilterType = "PERCENT_PRICE"
	PriceFilter      FilterType = "PRICE_FILTER"
)

type Asset string

const (
	Busd Asset = "BUSD"
	Usdt Asset = "USDT"
)

type OrderType string

const (
	Limit              OrderType = "LIMIT"
	Market             OrderType = "MARKET"
	Stop               OrderType = "STOP"
	StopMarket         OrderType = "STOP_MARKET"
	TakeProfit         OrderType = "TAKE_PROFIT"
	TakeProfitMarket   OrderType = "TAKE_PROFIT_MARKET"
	TrailingStopMarket OrderType = "TRAILING_STOP_MARKET"
)

type Status string

const (
	Trading Status = "TRADING"
)

type TimeInForce string

const (
	Fok TimeInForce = "FOK"
	Gtc TimeInForce = "GTC"
	Gtx TimeInForce = "GTX"
	Ioc TimeInForce = "IOC"
)

type UnderlyingSubType string

const (
	Defi UnderlyingSubType = "DEFI"
)

type UnderlyingType string

const (
	Coin  UnderlyingType = "COIN"
	Index UnderlyingType = "INDEX"
)
