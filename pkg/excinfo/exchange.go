package excinfo

import "strings"

func GetFuturesSymbols() []string {
	info := GetFuturesExcInfo()
	var symbols []string
	for _, symbol := range info {
		if strings.Contains(symbol.Symbol, "BUSD") {
			continue
		}
		if symbol.ContractType == "PERPETUAL" && symbol.Status == "TRADING" {
			symbols = append(symbols, symbol.Symbol)
		}
	}
	return symbols
}

func GetSpotSymbols(market string) []string {
	info := GetSpotExcInfo()
	var btc []string
	var usdt []string
	var bnb []string
	var eth []string
	var busd []string
	for _, symbol := range info {
		if symbol.Status == "TRADING" {
			if symbol.QuoteAsset == "BTC" {
				//if len(btc) < 10 {
				//	btc = append(btc, symbol.Symbol)
				//}
				btc = append(btc, symbol.Symbol)
			}
			if symbol.QuoteAsset == "BNB" {
				bnb = append(bnb, symbol.Symbol)
			}
			if symbol.QuoteAsset == "USDT" {
				usdt = append(usdt, symbol.Symbol)
			}
			if symbol.QuoteAsset == "BUSD" {
				busd = append(usdt, symbol.Symbol)
			}
			if symbol.QuoteAsset == "ETH" {
				eth = append(eth, symbol.Symbol)
			}
		}
	}

	switch strings.ToUpper(market) {
	case "BTC":
		return btc
	case "BNB":
		return bnb
	case "ETH":
		return eth
	case "USDT":
		return usdt
	case "BUSD":
		return busd
	default:
		return btc
	}
}
