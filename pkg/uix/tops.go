package uix

import (
	"fmt"
	ui "github.com/gizak/termui/v3"
	"github.com/gizak/termui/v3/widgets"
	"strings"
	"time"
)

var INTERVAL = []string{"1m", "5m", "15m"}

const topWidth = 20
const topHeight = 10
const tradeWidth = 85

type Table struct {
	id    string
	title string
	r     []int
}

func InitTops(intervals []time.Duration) map[string]*widgets.Table {
	var tables []Table
	for i := 0; i < len(intervals); i++ {
		aa := intervals[i]
		// g for gainer
		gid := fmt.Sprintf("g%s", aa)
		gt := fmt.Sprintf("Top Gainer %s", aa)
		gt = strings.Replace(gt, "0s", "", 1)
		gt = strings.Replace(gt, "0m", "", 1)
		// l for loser
		lid := fmt.Sprintf("l%s", aa)
		lt := fmt.Sprintf("Top Loser %s", aa)
		lt = strings.Replace(lt, "0s", "", 1)
		lt = strings.Replace(lt, "0m", "", 1)
		tables = append(tables, Table{
			id:    gid,
			title: gt,
			r:     []int{0, topHeight * i, topWidth, topHeight * (i + 1)},
		}, Table{
			id:    lid,
			title: lt,
			r:     []int{topWidth, topHeight * i, topWidth + topWidth, topHeight * (i + 1)},
		})
	}

	var tableMap = make(map[string]*widgets.Table)
	for _, s := range tables {
		tableMap[s.id] = widgets.NewTable()
		tableMap[s.id].TitleStyle = ui.NewStyle(ui.ColorYellow)
		tableMap[s.id].TextStyle = ui.NewStyle(ui.ColorWhite)
		tableMap[s.id].TextAlignment = ui.AlignCenter
		tableMap[s.id].RowSeparator = false
		tableMap[s.id].Title = s.title
		tableMap[s.id].SetRect(s.r[0], s.r[1], s.r[2], s.r[3])
	}
	return tableMap
}

type Kv struct {
	Key   string
	Value float64
}

const CoinCountPerTable = 8
