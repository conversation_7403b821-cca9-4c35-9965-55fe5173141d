package common

import (
	"math"
	"strconv"
	"strings"
)

func SplitTags(tags string) map[string]string {
	tagsSplit := strings.Split(tags, ",")
	tagsMap := map[string]string{}

	for _, t := range tagsSplit {
		if t != "" {
			kv := strings.Split(t, "=")

			if len(kv) == 2 {
				tagsMap[kv[0]] = kv[1]
			}
		}
	}

	return tagsMap
}

func StringToFloat2Decimal(str string) float64 {
	float, _ := strconv.ParseFloat(str, 64)
	return math.Round(float*100) / 100
}
