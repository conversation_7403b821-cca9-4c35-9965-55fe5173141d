package history

import (
	"encoding/json"
	"github.com/yudaprama/binance/pkg/trade"
)

func UnmarshalOrderHistory(data []byte) (OrderHistory, error) {
	var r OrderHistory
	err := json.Unmarshal(data, &r)
	return r, err
}

func (r *OrderHistory) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

type OrderHistory struct {
	Code          string      `json:"code"`
	Message       interface{} `json:"message"`
	MessageDetail interface{} `json:"messageDetail"`
	Data          []Datum     `json:"data"`
	Total         int64       `json:"total"`
	Success       bool        `json:"success"`
}

type Datum struct {
	OrderID           int64                  `json:"orderId"`
	OrderIDStr        string                 `json:"orderIdStr"`
	AccountID         interface{}            `json:"accountId"`
	AccountIDStr      interface{}            `json:"accountIdStr"`
	UserID            int64                  `json:"userId"`
	UserIDStr         string                 `json:"userIdStr"`
	Symbol            string                 `json:"symbol"`
	ClientOrderID     string                 `json:"clientOrderId"`
	OrigClientOrderID interface{}            `json:"origClientOrderId"`
	Price             string                 `json:"price"`
	OrigQty           string                 `json:"origQty"`
	ExecutedQty       string                 `json:"executedQty"`
	ExecutedQuoteQty  string                 `json:"executedQuoteQty"`
	Status            trade.Status           `json:"status"`
	OrigStatus        trade.Status           `json:"origStatus"`
	TimeInForce       interface{}            `json:"timeInForce"`
	Type              trade.Type             `json:"type"`
	Side              trade.Side             `json:"side"`
	StopPrice         float64                `json:"stopPrice"`
	Time              int64                  `json:"time"`
	UpdateTime        interface{}            `json:"updateTime"`
	BaseAsset         string                 `json:"baseAsset"`
	QuoteAsset        string                 `json:"quoteAsset"`
	DelegateMoney     string                 `json:"delegateMoney"`
	ExecutedPrice     string                 `json:"executedPrice"`
	ProductName       interface{}            `json:"productName"`
	MatchingUnitType  trade.MatchingUnitType `json:"matchingUnitType"`
	OrderType         trade.OrderType        `json:"orderType"`
	Language          trade.Language         `json:"language"`
	HasDetail         bool                   `json:"hasDetail"`
	StatusCode        string                 `json:"statusCode"`
	Email             interface{}            `json:"email"`
	OrderListID       int64                  `json:"orderListId"`
	MsgAuth           trade.MsgAuth          `json:"msgAuth"`
}
