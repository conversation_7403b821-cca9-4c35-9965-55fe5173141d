package history

import (
	"bytes"
	"encoding/json"
	"github.com/yudaprama/binance/pkg/trade"
	"io/ioutil"
	"log"
	"net/http"
	"time"
)

type Payload struct {
	Starttime   int64  `json:"startTime"`
	Endtime     int64  `json:"endTime"`
	Accounttype string `json:"accountType"`
	Page        int    `json:"page"`
	Rows        int    `json:"rows"`
}

// GetAllOrderHistory get all order history
func GetAllOrderHistory(accountType string) (data []Datum) {
	now := time.Now()
	ago := now.AddDate(0, -4, 0)
	for i := 1; i < 3; i++ {
		data = append(data, getSinglePage(i, now, ago, accountType)...)
	}
	return data
}

func getSinglePage(page int, now time.Time, ago time.Time, accountType string) []Datum {
	data := Payload{
		Starttime:   ago.Unix(),
		Endtime:     now.Unix(),
		Accounttype: accountType,
		Page:        page,
		Rows:        1000,
	}
	payloadBytes, err := json.Marshal(data)
	if err != nil {
		log.Fatalln(err)
	}
	body := bytes.NewReader(payloadBytes)

	req, err := http.NewRequest("POST", "https://www.binance.com/bapi/capital/v1/private/streamer/order/get-trade-orders", body)
	if err != nil {
		log.Fatalln(err)
	}
	req.Header.Set("Authority", "www.binance.com")
	req.Header.Set("X-Trace-Id", "211a41af-6972-440e-a7fc-4ee1396040d4")
	req.Header.Set("Csrftoken", "946cb03b8edf11f899b9340299e7a905")
	req.Header.Set("X-Ui-Request-Trace", "211a41af-6972-440e-a7fc-4ee1396040d4")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.115 Safari/537.36")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Lang", "en")
	req.Header.Set("Fvideo-Id", "317e7ea8f8cba7e1b865332be3baaffaf3f75700")
	req.Header.Set("Device-Info", "eyJzY3JlZW5fcmVzb2x1dGlvbiI6IjkwMCwxNDQwIiwiYXZhaWxhYmxlX3NjcmVlbl9yZXNvbHV0aW9uIjoiODc3LDE0NDAiLCJzeXN0ZW1fdmVyc2lvbiI6Ik1hYyBPUyAxMC4xNS43IiwiYnJhbmRfbW9kZWwiOiJ1bmtub3duIiwic3lzdGVtX2xhbmciOiJlbi1VUyIsInRpbWV6b25lIjoiR01UKzciLCJ0aW1lem9uZU9mZnNldCI6LTQyMCwidXNlcl9hZ2VudCI6Ik1vemlsbGEvNS4wIChNYWNpbnRvc2g7IEludGVsIE1hYyBPUyBYIDEwXzE1XzcpIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS85Mi4wLjQ1MTUuMTE1IFNhZmFyaS81MzcuMzYiLCJsaXN0X3BsdWdpbiI6IkNocm9tZSBQREYgUGx1Z2luLENocm9tZSBQREYgVmlld2VyIiwiY2FudmFzX2NvZGUiOiJmYWQzOTE4MSIsIndlYmdsX3ZlbmRvciI6Ikdvb2dsZSBJbmMuIiwid2ViZ2xfcmVuZGVyZXIiOiJHb29nbGUgU3dpZnRTaGFkZXIiLCJhdWRpbyI6IjEyNC4wNDM0NzY1NzgwODEwMyIsInBsYXRmb3JtIjoiTWFjSW50ZWwiLCJ3ZWJfdGltZXpvbmUiOiJBc2lhL0pha2FydGEiLCJkZXZpY2VfbmFtZSI6IkNocm9tZSBWOTIuMC40NTE1LjExNSAoTWFjIE9TKSIsImZpbmdlcnByaW50IjoiYTZmNzFhMjllMmFjOGYxOTg5ZDVmYjY1OTViZjBkMGQiLCJkZXZpY2VfaWQiOiIiLCJyZWxhdGVkX2RldmljZV9pZHMiOiIifQ==")
	req.Header.Set("Bnc-Uuid", "119e897e-8302-477d-8a12-c4c1ea601779")
	req.Header.Set("Clienttype", "web")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Sec-Gpc", "1")
	req.Header.Set("Origin", "https://www.binance.com")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Referer", "https://www.binance.com/en/trade/BTC_USDT?theme=dark&type=spot")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cookie", "cid=PDWv57Vg; bnc-uuid=119e897e-8302-477d-8a12-c4c1ea601779; source=referral; campaign=accounts.binance.com; fiat-prefer-currency=USD; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22********%22%2C%22first_id%22%3A%2217a3524f16653f-0428ce9fc5aab5-********-1296000-17a3524f167bec%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2217a3524f16653f-0428ce9fc5aab5-********-1296000-17a3524f167bec%22%7D; _ga=GA1.2.**********.**********; crypto_deposit_refactor=85; _gid=GA1.2.*********.**********; defaultMarketTab=futures; cr00=7B945F7714CEE0629AACB7B5B78998D4; d1og=web.********.7E4CA2F12318A64EAF25B6851F8D9BE3; r2o1=web.********.D1A47706B80082C7BB9299A9D9D59F7A; f30l=web.********.E113F933543CBB2312DE2346B9D62F30; logined=y; p20t=web.********.A1E4F287F5D7D0A6D6DDF621E038D897; userPreferredCurrency=USD_USD; monitor-uuid=3df9a0c1-3385-4dc8-a5e1-a890f013cc77; BNC_FV_KEY=317e7ea8f8cba7e1b865332be3baaffaf3f75700; BNC_FV_KEY_EXPIRE=1627615955967; _gat=1; lang=en")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Fatalln(err)
	}
	defer resp.Body.Close()
	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Fatalln(err)
	}
	history, err := UnmarshalOrderHistory(bodyBytes)
	if err != nil {
		log.Fatalln(err)
	}

	var a []Datum
	for _, d := range history.Data {
		if d.Side == trade.Buy && d.Status == trade.Filled {
			a = append(a, d)
		}
	}

	return a
}
