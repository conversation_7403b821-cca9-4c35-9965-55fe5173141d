# .goreleaser.yml
builds:
  - env:
      - CGO_ENABLED=0
    main: ./cmd/ws
    id: ws
    binary: ws
    goos:
      - darwin
      - linux
      - windows
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    main: ./cmd/create_order
    id: create_order
    binary: create_order
    goos:
      - darwin
      - linux
      - windows
    goarch:
      - amd64
  - env:
      - CGO_ENABLED=0
    main: ./cmd/tstorage
    id: tstorage
    binary: tstorage
    goos:
      - darwin
      - linux
      - windows
    goarch:
      - amd64
archives:
  - id: tgz
    format: tar.gz
    format_overrides:
      - goos: windows
        format: zip
