package main

import (
	"context"
	"github.com/adshao/go-binance/v2"
	"github.com/yudaprama/binance/pkg/trade"
	"log"
	"strings"
)

func main() {
	t := trade.NewBinanceClient()
	sells, err := t.Spot.NewListOpenOrdersService().Do(context.Background())
	if err != nil {
		log.Fatalln(err)
	}
	for _, sell := range sells {
		if (sell.Side == binance.SideTypeSell) && strings.Contains(sell.Symbol, "ETH") {
			a, err := t.Spot.NewCancelOrderService().Symbol(sell.Symbol).
				OrderID(sell.OrderID).Do(t.Ctx)
			if err != nil {
				log.Fatalln(err)
			}
			_, _, err = t.SellMarket(a.OrigQuantity, a.Symbol)
			if err != nil {
				log.Fatalln(err)
			}
		}
	}
}
