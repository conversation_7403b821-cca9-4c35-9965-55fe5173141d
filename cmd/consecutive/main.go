package main

import (
	"context"
	"fmt"
	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"
	"github.com/dariubs/percent"
	"github.com/yudaprama/binance/pkg/helper"
	"strconv"
)

var (
	apiKey      = "ERCO33oY2SCBqpGVxS60B38WYDOHWMgNAFAZC7c5ypYAZOlqkdK7uINsbQxAeMmG"
	secretKey   = "XhjabLZoLve0VdZQQgkgaRXFgntijuQ5xXl6HDsNsINYzwZH4reM9IwANcSPTOw3"
	consecutive = 4
)

func main() {
	helper.LoadEnv()
	client := binance.NewFuturesClient(apiKey, secretKey)

	exchangeInfo, err := client.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		panic(err)
	}

	intervals := helper.Environment.Interval
	interval := intervals[0]
	for {
		for _, symbol := range exchangeInfo.Symbols {
			printing(symbol, interval, client)
		}
	}
}

func printing(symbol futures.Symbol, interval string, client *futures.Client) {
	bullBear, totalPercentChange, totalConsecutive := reverse(symbol, interval, client)
	if totalConsecutive > 3 && totalConsecutive < 10 && totalPercentChange > 1 {
		var sign = ""
		if bullBear == 2 {
			sign = "-"
		}
		fmt.Printf("%vx %s %s%v\n", totalConsecutive, symbol.Symbol, sign, totalPercentChange)
	}
}

func reverse(symbol futures.Symbol, interval string, client *futures.Client) (int, float64, int) {
	klines, err := client.NewKlinesService().Symbol(symbol.Symbol).
		Interval(interval).Do(context.Background())
	if err != nil {
		return 0, 0, 0
	}
	var bullBear int
	var totalPercentChange float64 = 0
	var totalConsecutive = 1
	for i := len(klines) - 1; i >= 0; i-- {
		k := klines[i]
		c, _ := strconv.ParseFloat(k.Close, 64)
		o, _ := strconv.ParseFloat(k.Open, 64)
		ch := percent.ChangeFloat(o, c)

		if bullBear == 0 { // last candle
			if ch > 0 {
				bullBear = 1
				totalPercentChange += ch
			} else if ch < 0 {
				bullBear = 2
				totalPercentChange -= ch
			}
			totalConsecutive++
		} else { // except last candle
			if bullBear == 1 && ch > 0 {
				totalPercentChange += ch
				totalConsecutive++
			} else if bullBear == 2 && ch < 0 {
				totalPercentChange -= ch
				totalConsecutive++
			} else {
				break
			}
		}
	}
	return bullBear, totalPercentChange, totalConsecutive
}

func firstOpposite(symbol string, interval string, client *binance.Client) (int, float64, string) {
	klines, err := client.NewKlinesService().Symbol(symbol).
		Interval(interval).Do(context.Background())
	if err != nil {
		return 0, 0, ""
	}
	last := len(klines)
	a := 0
	var d string
	var b float64 = 0
	first := last - consecutive - 1
	for i := first; i < last; i++ {
		k := klines[i]
		c, _ := strconv.ParseFloat(k.Close, 64)
		o, _ := strconv.ParseFloat(k.Open, 64)
		ch := percent.ChangeFloat(o, c)
		if i == first {
			if ch > 0 {
				d = "positive"
			} else if ch < 0 {
				d = "negative"
			}
		} else {
			if ch > 0 {
				a++
				b += ch
			} else if ch < 0 {
				a--
				b -= ch
			}
		}
	}
	return a, b, d
}
