package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math/big"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/dariubs/percent"
	ui "github.com/gizak/termui/v3"
	"github.com/gizak/termui/v3/widgets"
	"github.com/nakabonne/tstorage"
	"github.com/yudaprama/binance/pkg/conf"
	"github.com/yudaprama/binance/pkg/excinfo"
	"github.com/yudaprama/binance/pkg/uix"
)

func main() {
	f := newFinance()
	defer f.storage.Close()

	// Start WebSocket connections with connection pooling
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	f.startWebSocketConnections(ctx)

	if err := ui.Init(); err != nil {
		log.Fatalf("failed to initialize termui: %v", err)
	}
	defer ui.Close()

	go f.render()

	uiEvents := ui.PollEvents()
	for {
		e := <-uiEvents
		switch e.ID {
		case "q", "<C-c>":
			cancel() // Cancel all WebSocket connections
			return
		}
	}
}

type finance struct {
	sync.RWMutex
	sync.WaitGroup
	client         *futures.Client
	symbols        []string
	storage        tstorage.Storage
	m1             time.Duration
	m3             time.Duration
	m5             time.Duration
	m15            time.Duration
	h1             time.Duration
	h4             time.Duration
	h5             time.Duration

	// Performance optimization fields
	eventBuffer    chan *futures.WsKlineEvent
	lastUpdate     int64 // atomic timestamp
	dataCache      map[string]map[time.Duration]float64
	cacheMutex     sync.RWMutex
	maxConnections int
}

func newFinance() *finance {
	storage, err := tstorage.NewStorage(
		tstorage.WithDataPath("./data"),
	)
	if err != nil {
		fmt.Println(err)
	}

	symbols := excinfo.GetFuturesSymbols()

	return &finance{
		symbols:        symbols,
		client:         futures.NewClient(conf.BinanceApiKey, conf.BinanceSecretKey),
		storage:        storage,
		m1:             time.Second * 5,
		m3:             time.Minute * 5,
		m5:             time.Minute * 3,
		m15:            time.Minute * 15,
		h1:             time.Hour * 1,
		h4:             time.Hour * 4,
		h5:             time.Hour * 24,
		eventBuffer:    make(chan *futures.WsKlineEvent, 1000), // Buffered channel
		dataCache:      make(map[string]map[time.Duration]float64),
		maxConnections: 50, // Limit concurrent connections
	}
}

// startWebSocketConnections manages WebSocket connections with connection pooling
func (f *finance) startWebSocketConnections(ctx context.Context) {
	// Start event processor
	go f.processEvents(ctx)

	// Create connection pools to limit concurrent connections
	semaphore := make(chan struct{}, f.maxConnections)

	for i, symbol := range f.symbols {
		// Add delay between connections to prevent overwhelming the server
		if i > 0 && i%10 == 0 {
			time.Sleep(100 * time.Millisecond)
		}

		go func(symbol string) {
			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			for {
				select {
				case <-ctx.Done():
					return
				default:
					doneC, _, err := futures.WsKlineServe(symbol, "1m", func(event *futures.WsKlineEvent) {
						select {
						case f.eventBuffer <- event:
						default:
							// Drop event if buffer is full to prevent blocking
						}
					}, f.errHandler)

					if err != nil {
						fmt.Printf("WebSocket error for %s: %v\n", symbol, err)
						time.Sleep(5 * time.Second) // Wait before reconnecting
						continue
					}

					select {
					case <-doneC:
						fmt.Printf("WebSocket connection closed for %s, reconnecting...\n", symbol)
						time.Sleep(1 * time.Second)
					case <-ctx.Done():
						return
					}
				}
			}
		}(symbol)
	}
}

// processEvents handles events from the buffer to reduce database write contention
func (f *finance) processEvents(ctx context.Context) {
	ticker := time.NewTicker(100 * time.Millisecond) // Batch process every 100ms
	defer ticker.Stop()

	var events []*futures.WsKlineEvent

	for {
		select {
		case <-ctx.Done():
			return
		case event := <-f.eventBuffer:
			events = append(events, event)
		case <-ticker.C:
			if len(events) > 0 {
				f.processBatchEvents(events)
				events = events[:0] // Reset slice
			}
		}
	}
}

// processBatchEvents processes multiple events in a single database transaction
func (f *finance) processBatchEvents(events []*futures.WsKlineEvent) {
	if len(events) == 0 {
		return
	}

	rows := make([]tstorage.Row, 0, len(events))
	now := time.Now().Unix()

	for _, event := range events {
		if event == nil {
			continue
		}

		price, err := strconv.ParseFloat(event.Kline.Close, 64)
		if err != nil {
			continue
		}

		rows = append(rows, tstorage.Row{
			Metric:    event.Symbol,
			DataPoint: tstorage.DataPoint{Timestamp: now, Value: price},
		})

		// Update cache
		f.updateCache(event.Symbol, price)
	}

	if len(rows) > 0 {
		if err := f.storage.InsertRows(rows); err != nil {
			fmt.Printf("Batch insert error: %v\n", err)
		}

		// Update last update timestamp
		atomic.StoreInt64(&f.lastUpdate, now)
	}
}

// updateCache updates the in-memory cache for faster queries
func (f *finance) updateCache(symbol string, price float64) {
	f.cacheMutex.Lock()
	defer f.cacheMutex.Unlock()

	if f.dataCache[symbol] == nil {
		f.dataCache[symbol] = make(map[time.Duration]float64)
	}

	// Store current price for immediate access
	f.dataCache[symbol][0] = price
}

// Removed unused klineHandler - now handled by processEvents

func (f *finance) errHandler(err error) {
	fmt.Println("errHandler", err)
}

// queryWithCache uses caching to reduce database queries
func (f *finance) queryWithCache(symbol string, dur time.Duration) float64 {
	// Check cache first
	f.cacheMutex.RLock()
	if cached, exists := f.dataCache[symbol]; exists {
		if value, found := cached[dur]; found {
			f.cacheMutex.RUnlock()
			return value
		}
	}
	f.cacheMutex.RUnlock()

	// If not in cache, query database
	result := f.query(symbol, dur)

	// Update cache
	f.cacheMutex.Lock()
	if f.dataCache[symbol] == nil {
		f.dataCache[symbol] = make(map[time.Duration]float64)
	}
	f.dataCache[symbol][dur] = result
	f.cacheMutex.Unlock()

	return result
}

func (f *finance) query(symbol string, dur time.Duration) float64 {
	now := time.Now()
	then := now.Add(-dur).Unix()
	points, err := f.storage.Select(symbol, nil, then, now.Unix())
	if errors.Is(err, tstorage.ErrNoDataPoints) {
		return 0
	}
	if err != nil {
		// Don't panic, just log and return 0
		fmt.Printf("Query error for %s: %v\n", symbol, err)
		return 0
	}
	if len(points) < 2 {
		return 0
	}
	thenPoint := points[0]
	nowPoint := points[len(points)-1]
	return percent.ChangeFloat(thenPoint.Value, nowPoint.Value)
}

// clearExpiredCache removes old cache entries to prevent memory leaks
func (f *finance) clearExpiredCache() {
	f.cacheMutex.Lock()
	defer f.cacheMutex.Unlock()

	// Clear cache every 5 minutes to ensure fresh data
	for symbol := range f.dataCache {
		f.dataCache[symbol] = make(map[time.Duration]float64)
	}
}

func (f *finance) render() {
	intervals := []time.Duration{f.m3, f.m15, f.h1, f.h4, f.h5}
	topMap := uix.InitTops(intervals)

	// Cache management ticker
	cacheTicker := time.NewTicker(5 * time.Minute)
	defer cacheTicker.Stop()

	// Render ticker - reduced frequency
	renderTicker := time.NewTicker(5 * time.Second) // Increased from 2s to 5s
	defer renderTicker.Stop()

	var lastRenderTime int64

	for {
		select {
		case <-cacheTicker.C:
			f.clearExpiredCache()

		case <-renderTicker.C:
			// Only render if we have new data
			currentUpdate := atomic.LoadInt64(&f.lastUpdate)
			if currentUpdate <= lastRenderTime {
				continue // Skip render if no new data
			}

			f.RLock()
			var uid []ui.Drawable

			topMapWithRows := f.generateTopRowsOptimized(topMap, intervals)
			if topMapWithRows != nil {
				for _, v := range topMap {
					uid = append(uid, v)
				}
				ui.Render(uid...)
				lastRenderTime = currentUpdate
			}
			f.RUnlock()
		}
	}
}

// Removed unused helper functions - replaced with optimized versions in optimizeSpecialCoins
// generateTopRowsOptimized is an optimized version with caching and reduced allocations
func (f *finance) generateTopRowsOptimized(topMap map[string]*widgets.Table, intervals []time.Duration) map[string]*widgets.Table {
	var mt = make(map[time.Duration][]uix.Kv)
	var aa int

	// Pre-allocate slices to reduce allocations
	symbolCount := len(f.symbols)

	for _, interval := range intervals {
		tg := make([]uix.Kv, 0, symbolCount)

		// Use goroutines for parallel processing of symbols (with limited concurrency)
		type result struct {
			key   string
			value float64
		}

		resultChan := make(chan result, symbolCount)
		semaphore := make(chan struct{}, 20) // Limit to 20 concurrent queries

		var wg sync.WaitGroup
		for _, symbol := range f.symbols {
			wg.Add(1)
			go func(sym string) {
				defer wg.Done()
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				ch := f.queryWithCache(sym, interval)
				resultChan <- result{
					key:   strings.Replace(sym, "USDT", "", 1),
					value: ch,
				}
			}(symbol)
		}

		// Close channel when all goroutines complete
		go func() {
			wg.Wait()
			close(resultChan)
		}()

		// Collect results
		for res := range resultChan {
			tg = append(tg, uix.Kv{Key: res.key, Value: res.value})
		}

		// Sort by value (descending)
		sort.Slice(tg, func(i, j int) bool {
			return tg[i].Value > tg[j].Value
		})

		// Optimize BTC and ETH positioning
		f.optimizeSpecialCoins(tg)

		mt[interval] = tg
		if len(mt[interval]) > uix.CoinCountPerTable {
			aa++
		}
	}

	return f.processTopRows(topMap, mt, intervals, aa)
}

// optimizeSpecialCoins moves BTC to top and ETH to bottom more efficiently
func (f *finance) optimizeSpecialCoins(tg []uix.Kv) {
	btcIdx := -1
	ethIdx := -1

	// Find indices in single pass
	for i, kv := range tg {
		if kv.Key == "BTC" {
			btcIdx = i
		} else if kv.Key == "ETH" {
			ethIdx = i
		}
		if btcIdx != -1 && ethIdx != -1 {
			break
		}
	}

	// Move BTC to top if found and not already there
	if btcIdx > 0 {
		btc := tg[btcIdx]
		copy(tg[1:btcIdx+1], tg[0:btcIdx])
		tg[0] = btc
	}

	// Move ETH to bottom if found and not already there
	if ethIdx != -1 && ethIdx < len(tg)-1 {
		eth := tg[ethIdx]
		copy(tg[ethIdx:len(tg)-1], tg[ethIdx+1:])
		tg[len(tg)-1] = eth
	}
}

// Removed old generateTopRows - replaced with generateTopRowsOptimized

// processTopRows handles the final processing of top rows data
func (f *finance) processTopRows(topMap map[string]*widgets.Table, mt map[time.Duration][]uix.Kv, intervals []time.Duration, aa int) map[string]*widgets.Table {
	if aa == len(intervals) {
		for interval, kvs := range mt {
			// top 8 gainer 1m
			var gainer [][]string
			for i := 0; i < uix.CoinCountPerTable; i++ {
				kvi := kvs[i]
				gainer = append(gainer, []string{kvi.Key, big.NewFloat(kvi.Value).SetMode(big.AwayFromZero).Text('f', 2)})
			}

			// top 8 loser 1m
			var loser [][]string
			for i := len(kvs) - 1; i >= len(kvs)-8; i-- {
				kvi := kvs[i]
				loser = append(loser, []string{kvi.Key, big.NewFloat(kvi.Value).SetMode(big.AwayFromZero).Text('f', 2)})
			}

			switch interval {
			case f.m1:
				topMap["g5s"].Rows = gainer
				topMap["l5s"].Rows = loser
			case f.m3:
				topMap["g5m0s"].Rows = gainer
				topMap["l5m0s"].Rows = loser
			case f.m5:
				topMap["g3m0s"].Rows = gainer
				topMap["l3m0s"].Rows = loser
			case f.m15:
				topMap["g15m0s"].Rows = gainer
				topMap["l15m0s"].Rows = loser
			case f.h1:
				topMap["g1h0m0s"].Rows = gainer
				topMap["l1h0m0s"].Rows = loser
			case f.h4:
				topMap["g4h0m0s"].Rows = gainer
				topMap["l4h0m0s"].Rows = loser
			case f.h5:
				topMap["g24h0m0s"].Rows = gainer
				topMap["l24h0m0s"].Rows = loser
			}
		}

		return topMap
	}

	return nil
}
