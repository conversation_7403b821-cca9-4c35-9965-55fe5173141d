package main

import (
	"errors"
	"fmt"
	"log"
	"math/big"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2/futures"
	"github.com/dariubs/percent"
	ui "github.com/gizak/termui/v3"
	"github.com/gizak/termui/v3/widgets"
	"github.com/nakabonne/tstorage"
	"github.com/yudaprama/binance/pkg/conf"
	"github.com/yudaprama/binance/pkg/excinfo"
	"github.com/yudaprama/binance/pkg/uix"
)

func main() {
	f := newFinance()
	defer f.storage.Close()
	for _, symbol := range f.symbols {
		go func(symbol string) {
			doneC, _, _ := futures.WsKlineServe(symbol, "1m", func(event *futures.WsKlineEvent) {
				f.kline<PERSON>andler(event)
			}, f.err<PERSON>andler)
			<-doneC
		}(symbol)
	}

	if err := ui.Init(); err != nil {
		log.Fatalf("failed to initialize termui: %v", err)
	}
	defer ui.Close()

	go f.render()

	uiEvents := ui.PollEvents()
	for {
		e := <-uiEvents
		switch e.ID {
		case "q", "<C-c>":
			return
		}
	}
}

type finance struct {
	sync.RWMutex
	sync.WaitGroup
	client  *futures.Client
	symbols []string
	storage tstorage.Storage
	m1      time.Duration
	m3      time.Duration
	m5      time.Duration
	m15     time.Duration
	h1      time.Duration
	h4      time.Duration
	h5      time.Duration
}

func newFinance() *finance {
	storage, err := tstorage.NewStorage(
		tstorage.WithDataPath("./data"),
	)
	if err != nil {
		fmt.Println(err)
	}
	return &finance{
		symbols: excinfo.GetFuturesSymbols(),
		client:  futures.NewClient(conf.BinanceApiKey, conf.BinanceSecretKey),
		storage: storage,
		m1:      time.Second * 5,
		m3:      time.Minute * 5,
		m5:      time.Minute * 3,
		m15:     time.Minute * 15,
		h1:      time.Hour * 1,
		h4:      time.Hour * 4,
		h5:      time.Hour * 24,
	}
}

func (f *finance) klineHandler(event *futures.WsKlineEvent) {
	float, err := strconv.ParseFloat(event.Kline.Close, 64)
	if err != nil {
		fmt.Println("strconv.ParseFloat", err)
	}
	if err := f.storage.InsertRows([]tstorage.Row{
		{Metric: event.Symbol, DataPoint: tstorage.DataPoint{Timestamp: time.Now().Unix(), Value: float}},
	}); err != nil {
		fmt.Println("f.storage.InsertRows", err)
	}
}

func (f *finance) errHandler(err error) {
	fmt.Println("errHandler", err)
}

func (f *finance) query(symbol string, dur time.Duration) float64 {
	now := time.Now()
	then := now.Add(-dur).Unix()
	points, err := f.storage.Select(symbol, nil, then, now.Unix())
	if errors.Is(err, tstorage.ErrNoDataPoints) {
		return 0
	}
	if err != nil {
		panic(err)
	}
	thenPoint := points[0]
	nowPoint := points[len(points)-1]
	return percent.ChangeFloat(thenPoint.Value, nowPoint.Value)
}

func (f *finance) render() {
	// No need to change
	intervals := []time.Duration{f.m3, f.m15, f.h1, f.h4, f.h5}
	topMap := uix.InitTops(intervals)

	// Stream to current data
	for {
		f.RLock()

		var uid []ui.Drawable

		topMapWithRows := f.generateTopRows(topMap, intervals)
		if topMapWithRows != nil {
			for _, v := range topMap {
				uid = append(uid, v)
			}
		}

		ui.Render(uid...)
		f.RUnlock()
		time.Sleep(2 * time.Second)
	}
}

func indexOf(element string, data []uix.Kv) int {
	for k, v := range data {
		if element == v.Key {
			return k
		}
	}
	return -1 //not found.
}

func insertInt(array []uix.Kv, value uix.Kv, index int) []uix.Kv {
	return append(array[:index], append([]uix.Kv{value}, array[index:]...)...)
}

func removeInt(array []uix.Kv, index int) []uix.Kv {
	return append(array[:index], array[index+1:]...)
}

func moveInt(array []uix.Kv, srcIndex int, dstIndex int) []uix.Kv {
	value := array[srcIndex]
	return insertInt(removeInt(array, srcIndex), value, dstIndex)
}
func (f *finance) generateTopRows(topMap map[string]*widgets.Table, intervals []time.Duration) map[string]*widgets.Table {
	var mt = make(map[time.Duration][]uix.Kv)
	var aa int
	for _, interval := range intervals {
		var tg []uix.Kv
		for _, symbol := range f.symbols {
			ch := f.query(symbol, interval)
			tg = append(tg, uix.Kv{Key: strings.Replace(symbol, "USDT", "", 1), Value: ch})
		}
		sort.Slice(tg, func(i, j int) bool {
			return tg[i].Value > tg[j].Value
		})
		moveInt(tg, indexOf("BTC", tg), 0)
		moveInt(tg, indexOf("ETH", tg), len(f.symbols)-1)
		mt[interval] = tg
		if len(mt[interval]) > uix.CoinCountPerTable {
			aa++
		}
	}

	if aa == len(intervals) {
		for interval, kvs := range mt {
			// top 8 gainer 1m
			var gainer [][]string
			for i := 0; i < uix.CoinCountPerTable; i++ {
				kvi := kvs[i]
				gainer = append(gainer, []string{kvi.Key, big.NewFloat(kvi.Value).SetMode(big.AwayFromZero).Text('f', 2)})
			}

			// top 8 loser 1m
			var loser [][]string
			for i := len(kvs) - 1; i >= len(kvs)-8; i-- {
				kvi := kvs[i]
				loser = append(loser, []string{kvi.Key, big.NewFloat(kvi.Value).SetMode(big.AwayFromZero).Text('f', 2)})
			}

			switch interval {
			case f.m1:
				topMap["g5s"].Rows = gainer
				topMap["l5s"].Rows = loser
			case f.m3:
				topMap["g5m0s"].Rows = gainer
				topMap["l5m0s"].Rows = loser
			case f.m5:
				topMap["g3m0s"].Rows = gainer
				topMap["l3m0s"].Rows = loser
			case f.m15:
				topMap["g15m0s"].Rows = gainer
				topMap["l15m0s"].Rows = loser
			case f.h1:
				topMap["g1h0m0s"].Rows = gainer
				topMap["l1h0m0s"].Rows = loser
			case f.h4:
				topMap["g4h0m0s"].Rows = gainer
				topMap["l4h0m0s"].Rows = loser
			case f.h5:
				topMap["g24h0m0s"].Rows = gainer
				topMap["l24h0m0s"].Rows = loser
			}
		}

		return topMap
	}

	return nil
}
