package main

import (
	"context"
	"fmt"
	"github.com/adshao/go-binance/v2"
)

func main() {
	apiKey := "ERCO33oY2SCBqpGVxS60B38WYDOHWMgNAFAZC7c5ypYAZOlqkdK7uINsbQxAeMmG"
	secretKey := "XhjabLZoLve0VdZQQgkgaRXFgntijuQ5xXl6HDsNsINYzwZH4reM9IwANcSPTOw3"

	client := binance.NewFuturesClient(apiKey, secretKey)

	exchangeInfo, err := client.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		panic(err)
	}

	for _, symbol := range exchangeInfo.Symbols {
		fmt.Println(symbol.Symbol)
	}
}
