package main

import (
	"context"
	"flag"
	"fmt"
	"github.com/adshao/go-binance/v2"
	"github.com/dariubs/percent"
	ui "github.com/gizak/termui/v3"
	"github.com/gizak/termui/v3/widgets"
	"github.com/yudaprama/binance/pkg/common"
	"github.com/yudaprama/binance/pkg/conf"
	"github.com/yudaprama/binance/pkg/excinfo"
	"github.com/yudaprama/binance/pkg/history"
	"github.com/yudaprama/binance/pkg/trade"
	"github.com/yudaprama/binance/pkg/uix"
	"log"
	"math/big"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

var INTERVAL = []string{"1m", "5m", "15m"}

type finance struct {
	sync.RWMutex
	sync.WaitGroup
	market        string
	accountType   string
	orders        []trade.Row
	pumps         map[string]int
	isSymbolExist map[string]struct{} // is symbol exist on sell order
	pairs1m       map[string]binance.WsKline
	pairs3m       map[string]binance.WsKline
	pairs5m       map[string]binance.WsKline
	pairs15m      map[string]binance.WsKline
	fromStart     map[string]binance.WsKline
	client        *binance.Client
	sort          string
	isPump        bool
	selectedAsset []string
}

func newFinance() *finance {
	f := &finance{
		pumps:         make(map[string]int),
		isSymbolExist: make(map[string]struct{}),
		pairs1m:       make(map[string]binance.WsKline),
		pairs3m:       make(map[string]binance.WsKline),
		pairs5m:       make(map[string]binance.WsKline),
		pairs15m:      make(map[string]binance.WsKline),
		fromStart:     make(map[string]binance.WsKline),
		client:        binance.NewClient(conf.BinanceApiKey, conf.BinanceSecretKey),
	}
	flag.StringVar(&f.market, "market", "BTC", "market")
	flag.StringVar(&f.sort, "sort", "", "sort")
	flag.BoolVar(&f.isPump, "pump", false, "sort")
	selectedAsset := flag.String("s", "", "selected asset")
	flag.StringVar(&f.accountType, "acc", "MAIN", "account type")
	flag.Parse()
	f.selectedAsset = strings.Split(*selectedAsset, ",")
	return f
}

// getOpenSellOrders get all open order (sell limit)
func (f *finance) getOpenSellOrders() []*binance.Order {
	openOrders, err := f.client.NewListOpenOrdersService().Do(context.Background())
	if err != nil {
		log.Fatal(err)
	}
	return openOrders
}

// setTrades get buy and sell (limit) from all trade
func (f *finance) setTrades() {
	buys := history.GetAllOrderHistory(f.accountType)
	sells := f.getOpenSellOrders()
	for _, sell := range sells {
		if sell.Side == binance.SideTypeSell {
			for _, buy := range buys {
				// match symbol and market
				if buy.Symbol == sell.Symbol && buy.QuoteAsset == f.market {
					sellQty := common.StringToFloat2Decimal(sell.OrigQuantity)
					buyQty := common.StringToFloat2Decimal(buy.OrigQty)
					if buyQty == sellQty {
						buyPrice, _ := strconv.ParseFloat(buy.ExecutedPrice, 64)
						sellPrice, _ := strconv.ParseFloat(sell.Price, 64)
						f.Lock()
						f.orders = append(f.orders, trade.Row{
							Info: trade.Info{
								Buy:  buyPrice,
								Sell: sellPrice,
							},
							Market: buy.QuoteAsset,
							Coin:   buy.BaseAsset,
							Symbol: buy.Symbol,
						})
						f.Unlock()
						// When found one, break to avoid duplicate
						break
					}
				}
			}
		}

		// Symbol is exist
		f.isSymbolExist[sell.Symbol] = struct{}{}
	}
}

func (f *finance) getPercentChange(k binance.WsKline) float64 {
	c, _ := strconv.ParseFloat(k.Close, 64)
	o, _ := strconv.ParseFloat(k.Open, 64)
	return percent.ChangeFloat(o, c)
}

func (f *finance) klineHandler(event *binance.WsKlineEvent, interval string) {
	market := strings.ReplaceAll(event.Symbol, f.market, "")
	f.Lock()
	switch interval {
	case "1m":
		f.pairs1m[market] = event.Kline
		if _, exist := f.fromStart[market]; !exist {
			f.fromStart[market] = event.Kline
		}
	case "3m":
		f.pairs3m[market] = event.Kline
	case "5m":
		f.pairs5m[market] = event.Kline
	case "15m":
		f.pairs15m[market] = event.Kline
	}
	f.Unlock()
}

func (f *finance) errHandler(err error) {
	fmt.Println("errHandler", err)
}

func main() {
	f := newFinance()

	f.setTrades()

	pairs := excinfo.GetSpotSymbols(f.market)
	for _, symbol := range pairs {
		for _, interval := range INTERVAL {
			go func(symbol, interval string) {
				doneC, _, _ := binance.WsKlineServe(symbol, interval, func(event *binance.WsKlineEvent) {
					f.klineHandler(event, interval)
				}, f.errHandler)
				//if err != nil {
				//	log.Fatalln("WsKlineServe with err:", err.Error())
				//}
				<-doneC
			}(symbol, interval)
		}
	}

	if err := ui.Init(); err != nil {
		log.Fatalf("failed to initialize termui: %v", err)
	}
	defer ui.Close()

	go f.render()

	uiEvents := ui.PollEvents()
	for {
		e := <-uiEvents
		switch e.ID {
		case "q", "<C-c>":
			return
		}
	}
}

func (f *finance) render() {
	// No need to change
	intervals := []time.Duration{time.Minute, time.Minute * 3, time.Minute * 5}
	topMap := uix.InitTops(intervals)
	orderMap := f.initTraceOrder(topWidth + topWidth)
	orderMap2 := f.initTraceOrder(topWidth + topWidth + tradeWidth)

	// Stream to current data
	for {
		f.RLock()

		var uid []ui.Drawable

		topMapWithRows := f.generateTopRows(topMap)
		if topMapWithRows != nil {
			for _, v := range topMap {
				uid = append(uid, v)
			}
		}

		uid = append(uid, f.generateOrderRows(orderMap, 0), f.generateOrderRows(orderMap2, 29))

		ui.Render(uid...)
		f.RUnlock()
		time.Sleep(2 * time.Second)
	}
}

const topWidth = 20
const topHeight = 10
const tradeWidth = 85

func (f *finance) initTraceOrder(x1 int) *widgets.Table {
	l := widgets.NewTable()
	l.Title = fmt.Sprintf("%s Trade", strings.ToUpper(f.market))
	l.TitleStyle = ui.NewStyle(ui.ColorYellow)
	l.TextAlignment = ui.AlignCenter
	l.SetRect(x1, 0, x1+tradeWidth, 62)
	return l
}

// generateTopRows generate percentage of change of top gainer & loser
func (f *finance) generateTopRows(topMap map[string]*widgets.Table) map[string]*widgets.Table {
	var mt = make(map[string][]uix.Kv)
	var aa int
	for _, interval := range INTERVAL {
		var tg []uix.Kv
		switch interval {
		case "1m":
			for k, v := range f.pairs1m {
				ch := f.getPercentChange(v)
				// when change more than 3%, we assume it is being pump
				// and below 4% so that we have spare profit left
				// so we should buy it
				closePrice, _ := strconv.ParseFloat(v.Close, 64)
				if f.isPump && (ch > 3) && (ch < 4) && (closePrice > 0.000001) {
					go f.buyPump(v)
				}
				tg = append(tg, uix.Kv{Key: k, Value: ch})
			}
		case "3m":
			for k, v := range f.pairs3m {
				tg = append(tg, uix.Kv{Key: k, Value: f.getPercentChange(v)})
			}
		case "5m":
			for k, v := range f.pairs5m {
				tg = append(tg, uix.Kv{Key: k, Value: f.getPercentChange(v)})
			}
		case "15m":
			for k, v := range f.pairs15m {
				tg = append(tg, uix.Kv{Key: k, Value: f.getPercentChange(v)})
			}
		}
		sort.Slice(tg, func(i, j int) bool {
			return tg[i].Value > tg[j].Value
		})
		mt[interval] = tg
		if len(mt[interval]) > uix.CoinCountPerTable {
			aa++
		}
	}

	if aa == len(INTERVAL) {
		for interval, kvs := range mt {
			// top 8 gainer 1m
			var gainer [][]string
			for i := 0; i < uix.CoinCountPerTable; i++ {
				kvi := kvs[i]
				gainer = append(gainer, []string{kvi.Key, big.NewFloat(kvi.Value).SetMode(big.AwayFromZero).Text('f', 2)})
			}

			// top 8 loser 1m
			var loser [][]string
			for i := len(kvs) - 1; i >= len(kvs)-8; i-- {
				kvi := kvs[i]
				loser = append(loser, []string{kvi.Key, big.NewFloat(kvi.Value).SetMode(big.AwayFromZero).Text('f', 2)})
			}

			switch interval {
			case "1m":
				topMap["g1m"].Rows = gainer
				topMap["l1m"].Rows = loser
			case "3m":
				topMap["g3m"].Rows = gainer
				topMap["l3m"].Rows = loser
			case "5m":
				topMap["g5m"].Rows = gainer
				topMap["l5m"].Rows = loser
			case "15m":
				topMap["g15m"].Rows = gainer
				topMap["l15m"].Rows = loser
			}
		}

		return topMap
	}

	return nil
}

type orderRows struct {
	coin           string
	market         string
	buy            float64
	sell           float64
	changeFromBuy  float64
	changeFromSell float64
	marketPrice    float64
}

func sliceContains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

// "Coin", "Buy", "Sell", "% From Buy", "% From Sell"
func (f *finance) generateOrderRows(orderMap *widgets.Table, from int) *widgets.Table {
	orderMap.Rows = [][]string{{"Coin", "Market", "Buy", "Sell", "% From Buy", "% From Sell"}}
	var s []orderRows
	for _, order := range f.orders {
		if val, exist := f.pairs1m[order.Coin]; exist {
			market := val.Close
			marketPrice, _ := strconv.ParseFloat(market, 64)
			ss := orderRows{
				coin:           order.Coin,
				market:         order.Market,
				buy:            order.Buy,
				sell:           order.Sell,
				marketPrice:    marketPrice,
				changeFromBuy:  percent.ChangeFloat(order.Buy, marketPrice),
				changeFromSell: -percent.ChangeFloat(marketPrice, order.Sell),
			}
			if len(f.selectedAsset) > 1 {
				if sliceContains(f.selectedAsset, order.Coin) {
					s = append(s, ss)
				}
			} else {
				s = append(s, ss)
			}
		}
	}
	sort.Slice(s, func(i, j int) bool {
		switch f.sort {
		case "DB":
			return s[i].changeFromBuy > s[j].changeFromBuy
		case "AB":
			return s[i].changeFromBuy < s[j].changeFromBuy
		case "DS":
			return s[i].changeFromSell > s[j].changeFromSell
		case "AS":
			return s[i].changeFromSell < s[j].changeFromSell
		case "DA":
			return s[i].coin > s[j].coin
		case "AA":
			return s[i].coin < s[j].coin
		default:
			return s[i].coin < s[j].coin
		}
	})
	if len(s) > from {
		for i := from; i < len(s); i++ {
			var (
				o           = s[i]
				buy         = fmt.Sprintf("%.8f", o.buy)
				pBuy        = fmt.Sprintf("%.2f", o.changeFromBuy)
				sell        = fmt.Sprintf("%.8f", o.sell)
				pSell       = fmt.Sprintf("%.2f", o.changeFromSell)
				marketPrice = fmt.Sprintf("%.8f", o.marketPrice)
			)
			var a = []string{o.coin, marketPrice, buy, sell, pBuy, pSell}
			orderMap.Rows = append(orderMap.Rows, a)
		}
	}
	return orderMap
}

func (f *finance) buyPump(v binance.WsKline) {
	// no redundant buying
	if inc, exist := f.pumps[v.Symbol]; exist && (inc == 1) {
		// make sure this is not pump dump 1 second
	} else if !exist {
		// don't buy pump if we have already have order
		if _, existOnSellOrder := f.isSymbolExist[v.Symbol]; !existOnSellOrder {
			t := trade.NewTrade()
			t.Print = false
			t.Symbol = v.Symbol
			t.Market = f.market
			t.SetAsset()
			t.SetMultiply()
			t.SetQtyByGivenAsset()

			f.Lock()
			f.orders = append(f.orders, t.Trading())
			f.pumps[v.Symbol]++
			f.isSymbolExist[v.Symbol] = struct{}{}
			f.Unlock()
		}
	} else if exist {
		f.pumps[v.Symbol]++
	}
}
