package main

import (
	"fmt"
	"github.com/adshao/go-binance/v2"
	"github.com/dariubs/percent"
	"github.com/yudaprama/binance/pkg/excinfo"
	"github.com/yudaprama/binance/pkg/helper"
	"strconv"
)

var (
	apiKey    = "ERCO33oY2SCBqpGVxS60B38WYDOHWMgNAFAZC7c5ypYAZOlqkdK7uINsbQxAeMmG"
	secretKey = "XhjabLZoLve0VdZQQgkgaRXFgntijuQ5xXl6HDsNsINYzwZH4reM9IwANcSPTOw3"
)

func init() {
	helper.LoadEnv()
}

func main() {
	symbols := excinfo.GetFuturesSymbols()
	for _, symbol := range symbols {
		for _, interval := range helper.Environment.Interval {
			go test(symbol, interval)
		}
	}
	fmt.Scanln()
}

func m135(interval string, symbol string, ch float64) {
	if (interval == "1m" && (ch > 1 || ch < -1)) || (interval == "3m" && (ch > 2.5 || ch < -2.5)) || (interval == "5m" && (ch > 3.5 || ch < -3.5)) {
		fmt.Println(symbol, interval, ch)
	}
}

func m15(interval string, symbol string, ch float64) {
	if ch > 6 || ch < -6 {
		fmt.Println(symbol, interval, ch)
	}
}

func test(symbol string, interval string) {
	wsKlineHandler := func(event *binance.WsKlineEvent) {
		k := event.Kline
		c, _ := strconv.ParseFloat(k.Close, 64)
		o, _ := strconv.ParseFloat(k.Open, 64)
		ch := percent.ChangeFloat(o, c)
		var threshold float64 = 1
		if interval == "1m" {
			threshold = 1
		}
		if interval == "3m" {
			threshold = 2.5
		}
		if interval == "5m" {
			threshold = 3.5
		}
		if interval == "15m" {
			threshold = 6
		}
		if ch > threshold || ch < -threshold {
			fmt.Println(symbol, interval, ch)
		}
	}
	errHandler := func(err error) {
		fmt.Println(err)
	}
	m1, _, err := binance.WsKlineServe(symbol, interval, wsKlineHandler, errHandler)
	if err != nil {
		fmt.Println(err)
		return
	}
	<-m1
}
